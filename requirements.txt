# 浏览器自动化核心库
DrissionPage>=4.0.0

# Web框架和API服务
fastapi>=0.104.0
uvicorn>=0.24.0

# 数据处理和验证
pydantic>=2.5.0
pydantic-settings>=2.1.0

# 日志和配置
loguru>=0.7.0
python-dotenv>=1.0.0

# 图像处理（用于截图功能）
Pillow>=10.0.0

# 异步支持
asyncio-mqtt>=0.13.0
aiofiles>=23.2.1

# 工具库
requests>=2.31.0
python-multipart>=0.0.6

# 爬虫相关依赖
beautifulsoup4>=4.12.0
httpx>=0.25.0
PyYAML>=6.0.0
lxml>=4.9.0

# 开发和测试依赖
pytest>=7.4.0
pytest-asyncio>=0.21.0
black>=23.0.0
flake8>=6.0.0
