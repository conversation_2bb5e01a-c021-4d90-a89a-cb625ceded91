# 起点爬虫项目架构设计方案 v4.0

## 项目概述

本项目是一个基于DrissionPage的高性能网络爬虫系统，采用模块化设计，将功能划分为三个核心分类：浏览器操作、信息获取、数据存储。系统支持指纹浏览器环境保存、OCR识别、多格式数据导出等高级功能。

## 系统架构

```
qidian-crawler-v4.0/
├── src/                          # 核心源码目录
│   ├── browser/                  # 浏览器操作分类
│   │   ├── __init__.py
│   │   ├── browser_manager.py    # 浏览器管理器
│   │   ├── page_controller.py    # 页面控制器
│   │   ├── element_locator.py    # 元素定位器
│   │   ├── screenshot_handler.py # 截图处理器
│   │   ├── fingerprint_manager.py# 指纹管理器
│   │   └── proxy_manager.py      # 代理管理器
│   ├── extractor/                # 信息获取分类
│   │   ├── __init__.py
│   │   ├── content_parser.py     # 内容解析器
│   │   ├── ocr_processor.py      # OCR处理器
│   │   ├── data_extractor.py     # 数据提取器
│   │   ├── novel_scraper.py      # 小说爬取器
│   │   └── image_analyzer.py     # 图像分析器
│   └── storage/                  # 数据存储分类
│       ├── __init__.py
│       ├── database_manager.py   # 数据库管理器
│       ├── file_handler.py       # 文件处理器
│       ├── export_manager.py     # 导出管理器
│       ├── backup_manager.py     # 备份管理器
│       └── cache_manager.py      # 缓存管理器
├── config/                       # 配置文件目录
├── data/                        # 数据存储目录
├── logs/                        # 日志目录
├── temp/                        # 临时文件目录
└── docs/                        # 文档目录
```

## 三大核心分类详解

### 1. 浏览器操作分类 (Browser)

#### 1.1 功能概述
提供完整的浏览器操作接口，基于DrissionPage框架，支持Chrome/Edge浏览器的各种操作，包括指纹浏览器环境的保存和恢复。

#### 1.2 核心模块

##### BrowserManager (浏览器管理器)
```python
class BrowserManager:
    """浏览器管理器 - 负责浏览器的创建、配置和生命周期管理"""
    
    def __init__(self, config: BrowserConfig):
        """初始化浏览器管理器"""
        
    def create_browser(self, profile_name: str = None) -> ChromiumPage:
        """创建浏览器实例"""
        
    def save_browser_profile(self, profile_name: str) -> bool:
        """保存浏览器配置文件（指纹信息）"""
        
    def load_browser_profile(self, profile_name: str) -> bool:
        """加载浏览器配置文件"""
        
    def get_browser_fingerprint(self) -> dict:
        """获取浏览器指纹信息"""
        
    def set_browser_fingerprint(self, fingerprint: dict) -> bool:
        """设置浏览器指纹信息"""
```

##### PageController (页面控制器)
```python
class PageController:
    """页面控制器 - 负责页面导航和基本操作"""
    
    def navigate_to(self, url: str) -> dict:
        """导航到指定URL"""
        
    def get_page_source(self) -> str:
        """获取页面源码"""
        
    def wait_for_load(self, timeout: int = 30) -> bool:
        """等待页面加载完成"""
        
    def execute_script(self, script: str) -> any:
        """执行JavaScript脚本"""
        
    def handle_alert(self, action: str = "accept") -> bool:
        """处理弹窗"""
```

##### ElementLocator (元素定位器)
```python
class ElementLocator:
    """元素定位器 - 负责页面元素的定位和操作"""
    
    def find_element(self, selector: str, method: str = "css") -> ChromiumElement:
        """查找单个元素"""
        
    def find_elements(self, selector: str, method: str = "css") -> List[ChromiumElement]:
        """查找多个元素"""
        
    def wait_for_element(self, selector: str, timeout: int = 10) -> ChromiumElement:
        """等待元素出现"""
        
    def click_element(self, element: ChromiumElement) -> bool:
        """点击元素"""
        
    def input_text(self, element: ChromiumElement, text: str) -> bool:
        """输入文本"""
```

##### ScreenshotHandler (截图处理器)
```python
class ScreenshotHandler:
    """截图处理器 - 负责页面和元素截图"""
    
    def capture_full_page(self, save_path: str = None) -> str:
        """截取整个页面"""
        
    def capture_element(self, element: ChromiumElement, save_path: str = None) -> str:
        """截取指定元素"""
        
    def capture_viewport(self, save_path: str = None) -> str:
        """截取可视区域"""
        
    def compare_screenshots(self, img1_path: str, img2_path: str) -> float:
        """比较两张截图的相似度"""
```

##### FingerprintManager (指纹管理器)
```python
class FingerprintManager:
    """指纹管理器 - 管理浏览器指纹信息"""
    
    def generate_fingerprint(self) -> dict:
        """生成随机浏览器指纹"""
        
    def save_fingerprint(self, name: str, fingerprint: dict) -> bool:
        """保存指纹配置"""
        
    def load_fingerprint(self, name: str) -> dict:
        """加载指纹配置"""
        
    def apply_fingerprint(self, fingerprint: dict) -> bool:
        """应用指纹配置到浏览器"""
        
    def get_current_fingerprint(self) -> dict:
        """获取当前浏览器指纹"""
```

#### 1.3 接口设计

```python
# 浏览器操作统一接口
class BrowserInterface:
    """浏览器操作统一接口"""
    
    # 基础操作
    def init_browser(self, config: dict) -> bool
    def close_browser(self) -> bool
    def navigate(self, url: str) -> dict
    def refresh_page(self) -> bool
    def go_back(self) -> bool
    def go_forward(self) -> bool
    
    # 元素操作
    def find_element(self, selector: str) -> dict
    def click(self, selector: str) -> bool
    def input_text(self, selector: str, text: str) -> bool
    def get_text(self, selector: str) -> str
    def get_attribute(self, selector: str, attr: str) -> str
    
    # 页面信息
    def get_source(self) -> str
    def get_title(self) -> str
    def get_url(self) -> str
    def get_cookies(self) -> list
    def set_cookies(self, cookies: list) -> bool
    
    # 截图功能
    def screenshot_page(self, path: str) -> str
    def screenshot_element(self, selector: str, path: str) -> str
    
    # 指纹管理
    def save_profile(self, name: str) -> bool
    def load_profile(self, name: str) -> bool
    def get_fingerprint(self) -> dict
    def set_fingerprint(self, fingerprint: dict) -> bool
```

### 2. 信息获取分类 (Extractor)

#### 2.1 功能概述
充分利用浏览器分类的各种接口，获取网页源码并解析内容，支持OCR识别、图像分析等高级功能。

#### 2.2 核心模块

##### ContentParser (内容解析器)
```python
class ContentParser:
    """内容解析器 - 负责HTML内容的解析和提取"""
    
    def parse_html(self, html: str) -> BeautifulSoup:
        """解析HTML内容"""
        
    def extract_text(self, html: str, selector: str = None) -> str:
        """提取文本内容"""
        
    def extract_links(self, html: str, base_url: str = None) -> List[str]:
        """提取链接"""
        
    def extract_images(self, html: str, base_url: str = None) -> List[str]:
        """提取图片链接"""
        
    def clean_text(self, text: str) -> str:
        """清理文本内容"""
```

##### OCRProcessor (OCR处理器)
```python
class OCRProcessor:
    """OCR处理器 - 负责图像文字识别"""
    
    def __init__(self, engine: str = "paddleocr"):
        """初始化OCR引擎"""
        
    def recognize_text(self, image_path: str) -> dict:
        """识别图片中的文字"""
        
    def recognize_element_text(self, element_screenshot: str) -> dict:
        """识别元素截图中的文字"""
        
    def batch_recognize(self, image_paths: List[str]) -> List[dict]:
        """批量识别文字"""
        
    def preprocess_image(self, image_path: str) -> str:
        """图像预处理"""
```

##### DataExtractor (数据提取器)
```python
class DataExtractor:
    """数据提取器 - 负责结构化数据提取"""
    
    def extract_novel_info(self, page_source: str) -> dict:
        """提取小说信息"""
        
    def extract_chapter_list(self, page_source: str) -> List[dict]:
        """提取章节列表"""
        
    def extract_chapter_content(self, page_source: str) -> dict:
        """提取章节内容"""
        
    def extract_user_info(self, page_source: str) -> dict:
        """提取用户信息"""
        
    def extract_comments(self, page_source: str) -> List[dict]:
        """提取评论信息"""
```

##### NovelScraper (小说爬取器)
```python
class NovelScraper:
    """小说爬取器 - 专门负责小说内容的爬取"""
    
    def __init__(self, browser_manager: BrowserManager):
        """初始化爬取器"""
        
    def search_novels(self, keyword: str, limit: int = 10) -> List[dict]:
        """搜索小说"""
        
    def get_novel_detail(self, novel_url: str) -> dict:
        """获取小说详情"""
        
    def get_chapter_list(self, novel_url: str) -> List[dict]:
        """获取章节列表"""
        
    def download_chapter(self, chapter_url: str) -> dict:
        """下载单个章节"""
        
    def batch_download(self, chapter_urls: List[str]) -> List[dict]:
        """批量下载章节"""
```

##### ImageAnalyzer (图像分析器)
```python
class ImageAnalyzer:
    """图像分析器 - 负责图像内容分析"""
    
    def analyze_captcha(self, image_path: str) -> dict:
        """分析验证码"""
        
    def detect_text_regions(self, image_path: str) -> List[dict]:
        """检测文本区域"""
        
    def extract_table_data(self, image_path: str) -> List[List[str]]:
        """从图片中提取表格数据"""
        
    def compare_images(self, img1_path: str, img2_path: str) -> float:
        """比较图片相似度"""
```

#### 2.3 接口设计

```python
# 信息获取统一接口
class ExtractorInterface:
    """信息获取统一接口"""
    
    # 内容解析
    def parse_page(self, url: str) -> dict
    def extract_text(self, selector: str) -> str
    def extract_links(self, selector: str) -> List[str]
    def extract_data(self, selectors: dict) -> dict
    
    # OCR识别
    def ocr_element(self, selector: str) -> dict
    def ocr_image(self, image_path: str) -> dict
    def ocr_region(self, coordinates: tuple) -> dict
    
    # 专业提取
    def extract_novel(self, novel_url: str) -> dict
    def extract_chapters(self, novel_url: str) -> List[dict]
    def extract_content(self, chapter_url: str) -> dict
    
    # 图像分析
    def analyze_image(self, image_path: str) -> dict
    def detect_captcha(self, image_path: str) -> dict
    def extract_table(self, image_path: str) -> List[dict]
```

### 3. 数据存储分类 (Storage)

#### 3.1 功能概述
负责数据的持久化存储，支持多种存储格式（SQLite、JSON、TXT、CSV），提供数据备份、导出、缓存等功能。

#### 3.2 核心模块

##### DatabaseManager (数据库管理器)
```python
class DatabaseManager:
    """数据库管理器 - 负责SQLite数据库操作"""
    
    def __init__(self, db_path: str):
        """初始化数据库连接"""
        
    def create_tables(self) -> bool:
        """创建数据表"""
        
    def save_novel(self, novel_data: dict) -> int:
        """保存小说信息"""
        
    def save_chapter(self, chapter_data: dict, novel_id: int) -> bool:
        """保存章节内容"""
        
    def get_novel(self, novel_id: int) -> dict:
        """获取小说信息"""
        
    def get_chapters(self, novel_id: int) -> List[dict]:
        """获取章节列表"""
        
    def search_novels(self, keyword: str) -> List[dict]:
        """搜索小说"""
        
    def get_statistics(self) -> dict:
        """获取统计信息"""
```

##### FileHandler (文件处理器)
```python
class FileHandler:
    """文件处理器 - 负责文件的读写操作"""
    
    def save_json(self, data: dict, file_path: str) -> bool:
        """保存JSON文件"""
        
    def load_json(self, file_path: str) -> dict:
        """加载JSON文件"""
        
    def save_text(self, content: str, file_path: str) -> bool:
        """保存文本文件"""
        
    def load_text(self, file_path: str) -> str:
        """加载文本文件"""
        
    def save_csv(self, data: List[dict], file_path: str) -> bool:
        """保存CSV文件"""
        
    def load_csv(self, file_path: str) -> List[dict]:
        """加载CSV文件"""
```

##### ExportManager (导出管理器)
```python
class ExportManager:
    """导出管理器 - 负责数据的多格式导出"""
    
    def export_to_txt(self, novel_id: int, output_path: str) -> bool:
        """导出为TXT格式"""
        
    def export_to_json(self, novel_id: int, output_path: str) -> bool:
        """导出为JSON格式"""
        
    def export_to_csv(self, novel_id: int, output_path: str) -> bool:
        """导出为CSV格式"""
        
    def export_to_epub(self, novel_id: int, output_path: str) -> bool:
        """导出为EPUB格式"""
        
    def batch_export(self, novel_ids: List[int], format: str, output_dir: str) -> dict:
        """批量导出"""
```

##### BackupManager (备份管理器)
```python
class BackupManager:
    """备份管理器 - 负责数据备份和恢复"""
    
    def create_backup(self, backup_name: str = None) -> str:
        """创建数据备份"""
        
    def restore_backup(self, backup_path: str) -> bool:
        """恢复数据备份"""
        
    def list_backups(self) -> List[dict]:
        """列出所有备份"""
        
    def delete_backup(self, backup_name: str) -> bool:
        """删除备份"""
        
    def auto_backup(self, interval_hours: int = 24) -> bool:
        """自动备份"""
```

##### CacheManager (缓存管理器)
```python
class CacheManager:
    """缓存管理器 - 负责数据缓存"""
    
    def set_cache(self, key: str, value: any, expire_time: int = 3600) -> bool:
        """设置缓存"""
        
    def get_cache(self, key: str) -> any:
        """获取缓存"""
        
    def delete_cache(self, key: str) -> bool:
        """删除缓存"""
        
    def clear_cache(self) -> bool:
        """清空缓存"""
        
    def cache_page(self, url: str, content: str) -> bool:
        """缓存页面内容"""
        
    def get_cached_page(self, url: str) -> str:
        """获取缓存的页面"""
```

#### 3.3 接口设计

```python
# 数据存储统一接口
class StorageInterface:
    """数据存储统一接口"""
    
    # 基础存储
    def save_data(self, data: dict, data_type: str) -> bool
    def load_data(self, data_id: str, data_type: str) -> dict
    def delete_data(self, data_id: str, data_type: str) -> bool
    def search_data(self, query: dict, data_type: str) -> List[dict]
    
    # 文件操作
    def save_file(self, content: str, file_path: str, format: str) -> bool
    def load_file(self, file_path: str) -> str
    def export_data(self, data_id: str, format: str, output_path: str) -> bool
    
    # 缓存操作
    def cache_set(self, key: str, value: any, ttl: int) -> bool
    def cache_get(self, key: str) -> any
    def cache_delete(self, key: str) -> bool
    def cache_clear(self) -> bool
    
    # 备份操作
    def create_backup(self, backup_name: str) -> bool
    def restore_backup(self, backup_path: str) -> bool
    def list_backups(self) -> List[dict]
```

## 模块间交互关系

### 数据流向图

```
用户请求 → 浏览器操作 → 信息获取 → 数据存储
    ↓           ↓           ↓           ↓
  终端界面   页面导航     内容解析     数据库存储
    ↓           ↓           ↓           ↓
  配置管理   元素定位     OCR识别     文件导出
    ↓           ↓           ↓           ↓
  日志记录   截图处理     数据提取     缓存管理
```

### 接口调用关系

```python
# 典型的爬取流程
class CrawlerWorkflow:
    """爬虫工作流程"""
    
    def __init__(self):
        self.browser = BrowserManager()
        self.extractor = DataExtractor()
        self.storage = DatabaseManager()
    
    async def crawl_novel(self, novel_url: str):
        """爬取小说的完整流程"""
        
        # 1. 浏览器操作 - 导航到页面
        page_result = self.browser.navigate_to(novel_url)
        if not page_result['success']:
            return False
        
        # 2. 信息获取 - 解析页面内容
        page_source = self.browser.get_page_source()
        novel_info = self.extractor.extract_novel_info(page_source)
        
        # 3. 数据存储 - 保存小说信息
        novel_id = self.storage.save_novel(novel_info)
        
        # 4. 获取章节列表
        chapters = self.extractor.extract_chapter_list(page_source)
        
        # 5. 批量下载章节
        for chapter in chapters:
            # 浏览器操作
            self.browser.navigate_to(chapter['url'])
            
            # 信息获取
            chapter_source = self.browser.get_page_source()
            chapter_content = self.extractor.extract_chapter_content(chapter_source)
            
            # 数据存储
            self.storage.save_chapter(chapter_content, novel_id)
        
        return True
```

## 配置系统设计

### 配置文件结构

```yaml
# config/config.yaml
browser:
  headless: false
  user_agent: "Mozilla/5.0 ..."
  window_size: [1920, 1080]
  timeout: 30
  proxy:
    enabled: false
    server: ""
    username: ""
    password: ""
  fingerprint:
    enabled: true
    profile_dir: "./profiles"
    
extractor:
  ocr:
    engine: "paddleocr"
    languages: ["ch", "en"]
    confidence_threshold: 0.8
  parser:
    encoding: "utf-8"
    timeout: 10
  scraper:
    delay_range: [1, 3]
    retry_count: 3
    concurrent_limit: 3
    
storage:
  database:
    type: "sqlite"
    path: "./data/novels.db"
    backup_enabled: true
  export:
    default_format: "txt"
    output_dir: "./exports"
  cache:
    enabled: true
    ttl: 3600
    max_size: "100MB"
```

## 错误处理和日志系统

### 错误处理策略

```python
class CrawlerException(Exception):
    """爬虫基础异常类"""
    pass

class BrowserException(CrawlerException):
    """浏览器操作异常"""
    pass

class ExtractorException(CrawlerException):
    """信息提取异常"""
    pass

class StorageException(CrawlerException):
    """存储操作异常"""
    pass

# 统一错误处理装饰器
def handle_errors(func):
    """错误处理装饰器"""
    def wrapper(*args, **kwargs):
        try:
            return func(*args, **kwargs)
        except Exception as e:
            logger.error(f"函数 {func.__name__} 执行失败: {str(e)}")
            return {"success": False, "error": str(e)}
    return wrapper
```

### 日志系统

```python
class LogManager:
    """日志管理器"""
    
    def __init__(self):
        self.setup_loggers()
    
    def setup_loggers(self):
        """设置分类日志器"""
        # 浏览器日志
        self.browser_logger = self.create_logger("browser", "logs/browser.log")
        # 提取器日志
        self.extractor_logger = self.create_logger("extractor", "logs/extractor.log")
        # 存储日志
        self.storage_logger = self.create_logger("storage", "logs/storage.log")
        # 主日志
        self.main_logger = self.create_logger("main", "logs/main.log")
```

## 性能优化策略

### 1. 并发控制
- 使用asyncio实现异步操作
- 限制并发数量避免被封IP
- 实现请求队列和速率限制

### 2. 缓存机制
- 页面内容缓存
- 解析结果缓存
- 图片和资源缓存

### 3. 资源管理
- 浏览器实例池
- 数据库连接池
- 内存使用监控

## 部署和维护

### 1. 环境要求
```
Python 3.8+
Chrome/Edge 浏览器
依赖包：
- DrissionPage
- BeautifulSoup4
- PaddleOCR
- SQLite3
- asyncio
```

### 2. 安装步骤
```bash
# 1. 克隆项目
git clone <project-url>

# 2. 安装依赖
pip install -r requirements.txt

# 3. 初始化配置
python setup_config.py

# 4. 运行爬虫
python start_crawler.py
```

### 3. 监控和维护
- 定期检查日志文件
- 监控系统资源使用
- 定期备份数据
- 更新浏览器驱动

## 扩展性设计

### 1. 插件系统
支持自定义插件扩展功能：
- 自定义解析器
- 自定义存储后端
- 自定义OCR引擎

### 2. 多站点支持
通过配置文件支持多个网站：
- 站点特定的解析规则
- 不同的反爬策略
- 独立的存储配置

### 3. API接口
提供REST API接口：
- 远程控制爬虫
- 数据查询接口
- 状态监控接口

## 总结

本设计方案将爬虫系统划分为三个核心分类，每个分类职责明确，接口清晰，便于开发和维护。通过模块化设计，系统具有良好的扩展性和可维护性，能够满足复杂的爬虫需求。