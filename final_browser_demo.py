"""
浏览器组件功能演示
展示所有正常工作的功能
"""

import sys
import os
import time
import json
from pathlib import Path

# 添加src目录到Python路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from browser import BrowserManager

def demo_browser_capabilities():
    """演示浏览器组件的完整能力"""
    print("🚀 浏览器组件功能演示")
    print("=" * 60)
    
    browser_manager = BrowserManager()
    
    try:
        # 1. 创建浏览器实例
        print("1. 创建浏览器实例...")
        page = browser_manager.create_browser(profile_name="demo_profile")
        print("   ✅ 浏览器创建成功")
        
        # 2. 获取浏览器指纹信息
        print("\n2. 获取浏览器指纹信息...")
        fingerprint = browser_manager.get_browser_fingerprint()
        print(f"   User-Agent: {fingerprint.get('userAgent', 'N/A')[:50]}...")
        print(f"   语言: {fingerprint.get('language', 'N/A')}")
        print(f"   平台: {fingerprint.get('platform', 'N/A')}")
        print(f"   屏幕: {fingerprint.get('screenWidth', 'N/A')}x{fingerprint.get('screenHeight', 'N/A')}")
        print(f"   时区: {fingerprint.get('timezone', 'N/A')}")
        print(f"   CPU核心: {fingerprint.get('hardwareConcurrency', 'N/A')}")
        
        # 3. 访问测试网站
        print("\n3. 访问测试网站...")
        result = browser_manager.navigate_to("https://httpbin.org/")
        if result.get("success"):
            print(f"   ✅ 成功访问: {result.get('title', 'N/A')}")
        else:
            print(f"   ❌ 访问失败: {result.get('error', 'N/A')}")
        
        # 4. 元素操作演示
        print("\n4. 元素操作演示...")
        # 查找页面元素
        body_element = page.ele("body")
        if body_element:
            print("   ✅ 成功定位body元素")
            text_content = body_element.text[:100] + "..." if len(body_element.text) > 100 else body_element.text
            print(f"   页面内容预览: {text_content}")
        
        # 查找所有链接
        links = page.eles("a")
        print(f"   ✅ 找到 {len(links)} 个链接")
        
        # 5. 截图功能演示
        print("\n5. 截图功能演示...")
        Path("screenshots").mkdir(exist_ok=True)
        screenshot_path = "screenshots/demo_screenshot.png"
        page.get_screenshot(screenshot_path)
        if Path(screenshot_path).exists():
            print(f"   ✅ 截图保存成功: {screenshot_path}")
        else:
            print("   ❌ 截图保存失败")
        
        # 6. JavaScript执行演示
        print("\n6. JavaScript执行演示...")
        js_result = page.run_js("""
            return {
                title: document.title,
                url: window.location.href,
                userAgent: navigator.userAgent.substring(0, 50) + '...',
                timestamp: new Date().toISOString(),
                screenInfo: {
                    width: screen.width,
                    height: screen.height,
                    colorDepth: screen.colorDepth
                }
            };
        """)
        
        if js_result:
            print("   ✅ JavaScript执行成功")
            print(f"   页面标题: {js_result.get('title', 'N/A')}")
            print(f"   当前URL: {js_result.get('url', 'N/A')}")
            print(f"   执行时间: {js_result.get('timestamp', 'N/A')}")
            screen_info = js_result.get('screenInfo', {})
            print(f"   屏幕信息: {screen_info.get('width')}x{screen_info.get('height')} ({screen_info.get('colorDepth')}位)")
        
        # 7. Cookie操作演示
        print("\n7. Cookie操作演示...")
        # 访问设置cookie的页面
        browser_manager.navigate_to("https://httpbin.org/cookies/set/demo_cookie/demo_value")
        time.sleep(1)
        
        # 检查cookie
        browser_manager.navigate_to("https://httpbin.org/cookies")
        time.sleep(1)
        source = browser_manager.get_page_source()
        if "demo_cookie" in source:
            print("   ✅ Cookie设置和读取成功")
        else:
            print("   ❌ Cookie操作失败")
        
        # 8. 配置文件保存演示
        print("\n8. 配置文件保存演示...")
        save_result = browser_manager.save_browser_profile("demo_profile")
        if save_result:
            print("   ✅ 配置文件保存成功")
            
            # 列出所有配置文件
            profiles = browser_manager.list_profiles()
            print(f"   当前配置文件: {profiles}")
        else:
            print("   ❌ 配置文件保存失败")
        
        # 9. 反检测能力演示
        print("\n9. 反检测能力演示...")
        detection_result = page.run_js("""
            return {
                webdriver: navigator.webdriver,
                chrome: typeof window.chrome,
                plugins: navigator.plugins.length,
                languages: navigator.languages,
                cookieEnabled: navigator.cookieEnabled,
                doNotTrack: navigator.doNotTrack
            };
        """)
        
        if detection_result:
            print("   反检测状态:")
            print(f"   - webdriver属性: {detection_result.get('webdriver', 'N/A')} (应为undefined)")
            print(f"   - chrome对象: {detection_result.get('chrome', 'N/A')} (应为object)")
            print(f"   - 插件数量: {detection_result.get('plugins', 'N/A')} (应>0)")
            print(f"   - 语言设置: {detection_result.get('languages', 'N/A')}")
            print(f"   - Cookie启用: {detection_result.get('cookieEnabled', 'N/A')}")
            
            # 评估反检测效果
            score = 0
            total = 0
            
            if detection_result.get('webdriver') is None:
                score += 1
            total += 1
            
            if detection_result.get('chrome') == 'object':
                score += 1
            total += 1
            
            if detection_result.get('plugins', 0) > 0:
                score += 1
            total += 1
            
            if detection_result.get('cookieEnabled') is True:
                score += 1
            total += 1
            
            print(f"   反检测评分: {score}/{total} ({score/total*100:.1f}%)")
        
        # 10. 性能统计
        print("\n10. 性能统计...")
        status = browser_manager.get_status()
        print(f"   浏览器状态: {'正常' if status['initialized'] else '异常'}")
        print(f"   当前配置: {status['current_profile']}")
        print(f"   当前页面: {status['current_url']}")
        
        return True
        
    except Exception as e:
        print(f"❌ 演示过程中发生错误: {str(e)}")
        return False
    finally:
        # 清理资源
        print("\n11. 清理资源...")
        browser_manager.close_browser()
        print("   ✅ 浏览器已关闭")

def demo_profile_persistence():
    """演示配置文件持久性"""
    print("\n" + "=" * 60)
    print("💾 配置文件持久性演示")
    print("=" * 60)
    
    browser_manager = BrowserManager()
    
    try:
        # 第一阶段：创建和保存状态
        print("第一阶段：创建和保存状态")
        page = browser_manager.create_browser(profile_name="persistence_demo")
        
        # 设置一些状态
        browser_manager.navigate_to("https://httpbin.org/cookies/set/persistent_test/12345")
        time.sleep(1)
        
        # 设置localStorage
        page.run_js("localStorage.setItem('demo_key', 'demo_value_' + Date.now());")
        stored_value = page.run_js("return localStorage.getItem('demo_key');")
        print(f"   设置localStorage: {stored_value}")
        
        # 保存配置文件
        browser_manager.save_browser_profile("persistence_demo")
        print("   ✅ 状态保存完成")
        
        # 关闭浏览器
        browser_manager.close_browser()
        print("   浏览器已关闭")
        
        # 第二阶段：重新加载和验证状态
        print("\n第二阶段：重新加载和验证状态")
        page = browser_manager.create_browser(profile_name="persistence_demo")
        browser_manager.load_browser_profile("persistence_demo")
        
        # 验证cookie
        browser_manager.navigate_to("https://httpbin.org/cookies")
        time.sleep(1)
        source = browser_manager.get_page_source()
        if "persistent_test" in source:
            print("   ✅ Cookie状态恢复成功")
        else:
            print("   ❌ Cookie状态恢复失败")
        
        # 验证localStorage
        restored_value = page.run_js("return localStorage.getItem('demo_key');")
        if restored_value == stored_value:
            print(f"   ✅ localStorage恢复成功: {restored_value}")
        else:
            print(f"   ❌ localStorage恢复失败: 期望{stored_value}, 实际{restored_value}")
        
        return True
        
    except Exception as e:
        print(f"❌ 持久性演示失败: {str(e)}")
        return False
    finally:
        browser_manager.close_browser()

def main():
    """主演示函数"""
    print("🎭 起点爬虫浏览器组件完整功能演示")
    print("演示时间:", time.strftime("%Y-%m-%d %H:%M:%S"))
    print("DrissionPage版本: 4.1.0.18")
    
    # 确保必要的目录存在
    Path("screenshots").mkdir(exist_ok=True)
    Path("profiles").mkdir(exist_ok=True)
    
    # 执行演示
    demos = [
        ("浏览器完整功能", demo_browser_capabilities),
        ("配置文件持久性", demo_profile_persistence)
    ]
    
    results = []
    for demo_name, demo_func in demos:
        try:
            result = demo_func()
            results.append((demo_name, result))
        except Exception as e:
            print(f"❌ {demo_name}演示异常: {str(e)}")
            results.append((demo_name, False))
    
    # 输出演示总结
    print("\n" + "=" * 60)
    print("📊 演示结果总结")
    print("=" * 60)
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for demo_name, result in results:
        status = "✅ 成功" if result else "❌ 失败"
        print(f"{demo_name}: {status}")
    
    print(f"\n演示成功率: {passed}/{total} ({passed/total*100:.1f}%)")
    
    if passed == total:
        print("🎉 所有功能演示成功！浏览器组件工作正常")
    else:
        print("⚠️ 部分功能存在问题，需要进一步检查")
    
    print("\n📁 生成的文件:")
    print("   - screenshots/demo_screenshot.png (页面截图)")
    print("   - profiles/demo_profile/ (浏览器配置文件)")
    print("   - profiles/persistence_demo/ (持久性测试配置)")

if __name__ == "__main__":
    main()
