"""
指纹管理器
管理浏览器指纹信息，实现指纹浏览器功能
"""

import json
import random
from pathlib import Path
from typing import Dict, List, Optional
import logging

logger = logging.getLogger("browser")


class FingerprintManager:
    """指纹管理器 - 管理浏览器指纹信息"""
    
    def __init__(self, fingerprint_dir: str = "./fingerprints"):
        """初始化指纹管理器
        
        Args:
            fingerprint_dir: 指纹文件存储目录
        """
        self.fingerprint_dir = Path(fingerprint_dir)
        self.fingerprint_dir.mkdir(exist_ok=True)
        
        # 预定义的指纹模板
        self.fingerprint_templates = self._load_fingerprint_templates()
        
        logger.info(f"指纹管理器初始化完成，存储目录: {self.fingerprint_dir}")
    
    def _load_fingerprint_templates(self) -> Dict:
        """加载指纹模板"""
        return {
            "windows_chrome": {
                "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
                "platform": "Win32",
                "language": "zh-CN",
                "languages": ["zh-CN", "zh", "en"],
                "screenWidth": 1920,
                "screenHeight": 1080,
                "screenColorDepth": 24,
                "timezoneOffset": -480,
                "timezone": "Asia/Shanghai",
                "hardwareConcurrency": 8,
                "maxTouchPoints": 0,
                "cookieEnabled": True,
                "doNotTrack": None
            },
            "windows_edge": {
                "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36 Edg/120.0.0.0",
                "platform": "Win32",
                "language": "zh-CN",
                "languages": ["zh-CN", "zh", "en"],
                "screenWidth": 1920,
                "screenHeight": 1080,
                "screenColorDepth": 24,
                "timezoneOffset": -480,
                "timezone": "Asia/Shanghai",
                "hardwareConcurrency": 8,
                "maxTouchPoints": 0,
                "cookieEnabled": True,
                "doNotTrack": None
            },
            "mac_chrome": {
                "userAgent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
                "platform": "MacIntel",
                "language": "zh-CN",
                "languages": ["zh-CN", "zh", "en"],
                "screenWidth": 1440,
                "screenHeight": 900,
                "screenColorDepth": 24,
                "timezoneOffset": -480,
                "timezone": "Asia/Shanghai",
                "hardwareConcurrency": 8,
                "maxTouchPoints": 0,
                "cookieEnabled": True,
                "doNotTrack": None
            }
        }
    
    def generate_fingerprint(self, template: str = "windows_chrome", randomize: bool = True) -> Dict:
        """生成随机浏览器指纹
        
        Args:
            template: 指纹模板名称
            randomize: 是否随机化部分参数
            
        Returns:
            Dict: 生成的指纹信息
        """
        try:
            if template not in self.fingerprint_templates:
                template = "windows_chrome"
            
            fingerprint = self.fingerprint_templates[template].copy()
            
            if randomize:
                # 随机化屏幕分辨率
                screen_resolutions = [
                    (1920, 1080), (1366, 768), (1440, 900), 
                    (1536, 864), (1600, 900), (1280, 720)
                ]
                width, height = random.choice(screen_resolutions)
                fingerprint["screenWidth"] = width
                fingerprint["screenHeight"] = height
                
                # 随机化硬件并发数
                fingerprint["hardwareConcurrency"] = random.choice([4, 6, 8, 12, 16])
                
                # 随机化颜色深度
                fingerprint["screenColorDepth"] = random.choice([24, 32])
                
                # 随机化像素深度
                fingerprint["screenPixelDepth"] = fingerprint["screenColorDepth"]
                
                # 随机化WebGL信息
                webgl_vendors = [
                    "Google Inc. (Intel)",
                    "Google Inc. (NVIDIA)",
                    "Google Inc. (AMD)",
                    "Google Inc."
                ]
                fingerprint["webglVendor"] = random.choice(webgl_vendors)
                
                webgl_renderers = [
                    "ANGLE (Intel, Intel(R) UHD Graphics 630 Direct3D11 vs_5_0 ps_5_0, D3D11)",
                    "ANGLE (NVIDIA, NVIDIA GeForce GTX 1060 Direct3D11 vs_5_0 ps_5_0, D3D11)",
                    "ANGLE (AMD, AMD Radeon RX 580 Direct3D11 vs_5_0 ps_5_0, D3D11)"
                ]
                fingerprint["webglRenderer"] = random.choice(webgl_renderers)
            
            logger.info(f"生成指纹成功，模板: {template}")
            return fingerprint
            
        except Exception as e:
            logger.error(f"生成指纹失败: {str(e)}")
            return {}
    
    def save_fingerprint(self, name: str, fingerprint: Dict) -> bool:
        """保存指纹配置
        
        Args:
            name: 指纹名称
            fingerprint: 指纹数据
            
        Returns:
            bool: 是否保存成功
        """
        try:
            fingerprint_file = self.fingerprint_dir / f"{name}.json"
            
            with open(fingerprint_file, 'w', encoding='utf-8') as f:
                json.dump(fingerprint, f, ensure_ascii=False, indent=2)
            
            logger.info(f"指纹保存成功: {name}")
            return True
            
        except Exception as e:
            logger.error(f"保存指纹失败: {str(e)}")
            return False
    
    def load_fingerprint(self, name: str) -> Dict:
        """加载指纹配置
        
        Args:
            name: 指纹名称
            
        Returns:
            Dict: 指纹数据
        """
        try:
            fingerprint_file = self.fingerprint_dir / f"{name}.json"
            
            if not fingerprint_file.exists():
                logger.warning(f"指纹文件不存在: {name}")
                return {}
            
            with open(fingerprint_file, 'r', encoding='utf-8') as f:
                fingerprint = json.load(f)
            
            logger.info(f"指纹加载成功: {name}")
            return fingerprint
            
        except Exception as e:
            logger.error(f"加载指纹失败: {str(e)}")
            return {}
    
    def list_fingerprints(self) -> List[str]:
        """列出所有保存的指纹
        
        Returns:
            List[str]: 指纹名称列表
        """
        try:
            fingerprints = []
            for file_path in self.fingerprint_dir.glob("*.json"):
                fingerprints.append(file_path.stem)
            
            return fingerprints
            
        except Exception as e:
            logger.error(f"列出指纹失败: {str(e)}")
            return []
    
    def delete_fingerprint(self, name: str) -> bool:
        """删除指纹配置
        
        Args:
            name: 指纹名称
            
        Returns:
            bool: 是否删除成功
        """
        try:
            fingerprint_file = self.fingerprint_dir / f"{name}.json"
            
            if fingerprint_file.exists():
                fingerprint_file.unlink()
                logger.info(f"指纹删除成功: {name}")
                return True
            else:
                logger.warning(f"指纹文件不存在: {name}")
                return False
                
        except Exception as e:
            logger.error(f"删除指纹失败: {str(e)}")
            return False
    
    def apply_fingerprint(self, page, fingerprint: Dict) -> bool:
        """应用指纹配置到浏览器
        
        Args:
            page: ChromiumPage实例
            fingerprint: 指纹数据
            
        Returns:
            bool: 是否应用成功
        """
        try:
            if not fingerprint:
                return False
            
            # 构建指纹设置脚本
            script_parts = []
            
            # 设置用户代理
            if fingerprint.get('userAgent'):
                script_parts.append(f"""
                Object.defineProperty(navigator, 'userAgent', {{
                    get: () => '{fingerprint["userAgent"]}',
                    configurable: true
                }});
                """)
            
            # 设置平台
            if fingerprint.get('platform'):
                script_parts.append(f"""
                Object.defineProperty(navigator, 'platform', {{
                    get: () => '{fingerprint["platform"]}',
                    configurable: true
                }});
                """)
            
            # 设置语言
            if fingerprint.get('language'):
                script_parts.append(f"""
                Object.defineProperty(navigator, 'language', {{
                    get: () => '{fingerprint["language"]}',
                    configurable: true
                }});
                """)
            
            # 设置语言列表
            if fingerprint.get('languages'):
                languages_str = json.dumps(fingerprint['languages'])
                script_parts.append(f"""
                Object.defineProperty(navigator, 'languages', {{
                    get: () => {languages_str},
                    configurable: true
                }});
                """)
            
            # 设置硬件并发数
            if fingerprint.get('hardwareConcurrency'):
                script_parts.append(f"""
                Object.defineProperty(navigator, 'hardwareConcurrency', {{
                    get: () => {fingerprint["hardwareConcurrency"]},
                    configurable: true
                }});
                """)
            
            # 设置最大触摸点数
            if fingerprint.get('maxTouchPoints') is not None:
                script_parts.append(f"""
                Object.defineProperty(navigator, 'maxTouchPoints', {{
                    get: () => {fingerprint["maxTouchPoints"]},
                    configurable: true
                }});
                """)
            
            # 设置Cookie启用状态
            if fingerprint.get('cookieEnabled') is not None:
                script_parts.append(f"""
                Object.defineProperty(navigator, 'cookieEnabled', {{
                    get: () => {str(fingerprint["cookieEnabled"]).lower()},
                    configurable: true
                }});
                """)
            
            # 设置DoNotTrack
            if fingerprint.get('doNotTrack') is not None:
                dnt_value = f"'{fingerprint['doNotTrack']}'" if fingerprint['doNotTrack'] else "null"
                script_parts.append(f"""
                Object.defineProperty(navigator, 'doNotTrack', {{
                    get: () => {dnt_value},
                    configurable: true
                }});
                """)
            
            # 设置屏幕信息
            if fingerprint.get('screenWidth') and fingerprint.get('screenHeight'):
                script_parts.append(f"""
                Object.defineProperty(screen, 'width', {{
                    get: () => {fingerprint["screenWidth"]},
                    configurable: true
                }});
                Object.defineProperty(screen, 'height', {{
                    get: () => {fingerprint["screenHeight"]},
                    configurable: true
                }});
                Object.defineProperty(screen, 'availWidth', {{
                    get: () => {fingerprint["screenWidth"]},
                    configurable: true
                }});
                Object.defineProperty(screen, 'availHeight', {{
                    get: () => {fingerprint["screenHeight"] - 40},
                    configurable: true
                }});
                """)
            
            # 设置颜色深度
            if fingerprint.get('screenColorDepth'):
                script_parts.append(f"""
                Object.defineProperty(screen, 'colorDepth', {{
                    get: () => {fingerprint["screenColorDepth"]},
                    configurable: true
                }});
                Object.defineProperty(screen, 'pixelDepth', {{
                    get: () => {fingerprint["screenColorDepth"]},
                    configurable: true
                }});
                """)
            
            # 设置时区偏移
            if fingerprint.get('timezoneOffset') is not None:
                script_parts.append(f"""
                Date.prototype.getTimezoneOffset = function() {{
                    return {fingerprint["timezoneOffset"]};
                }};
                """)
            
            # 设置WebGL信息
            if fingerprint.get('webglVendor') or fingerprint.get('webglRenderer'):
                webgl_vendor = fingerprint.get('webglVendor', '')
                webgl_renderer = fingerprint.get('webglRenderer', '')
                
                script_parts.append(f"""
                const getParameter = WebGLRenderingContext.prototype.getParameter;
                WebGLRenderingContext.prototype.getParameter = function(parameter) {{
                    if (parameter === 37445) {{
                        return '{webgl_vendor}';
                    }}
                    if (parameter === 37446) {{
                        return '{webgl_renderer}';
                    }}
                    return getParameter.call(this, parameter);
                }};
                """)
            
            # 执行所有脚本
            full_script = "\n".join(script_parts)
            if full_script:
                page.run_js(full_script)
            
            logger.info("指纹应用成功")
            return True
            
        except Exception as e:
            logger.error(f"应用指纹失败: {str(e)}")
            return False
    
    def get_current_fingerprint(self, page) -> Dict:
        """获取当前浏览器指纹
        
        Args:
            page: ChromiumPage实例
            
        Returns:
            Dict: 当前指纹信息
        """
        try:
            fingerprint_script = """
            return {
                userAgent: navigator.userAgent,
                language: navigator.language,
                languages: navigator.languages,
                platform: navigator.platform,
                cookieEnabled: navigator.cookieEnabled,
                doNotTrack: navigator.doNotTrack,
                hardwareConcurrency: navigator.hardwareConcurrency,
                maxTouchPoints: navigator.maxTouchPoints,
                screenWidth: screen.width,
                screenHeight: screen.height,
                screenColorDepth: screen.colorDepth,
                screenPixelDepth: screen.pixelDepth,
                timezoneOffset: new Date().getTimezoneOffset(),
                timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
                webglVendor: (() => {
                    try {
                        const canvas = document.createElement('canvas');
                        const gl = canvas.getContext('webgl') || canvas.getContext('experimental-webgl');
                        return gl ? gl.getParameter(gl.VENDOR) : '';
                    } catch(e) { return ''; }
                })(),
                webglRenderer: (() => {
                    try {
                        const canvas = document.createElement('canvas');
                        const gl = canvas.getContext('webgl') || canvas.getContext('experimental-webgl');
                        return gl ? gl.getParameter(gl.RENDERER) : '';
                    } catch(e) { return ''; }
                })()
            };
            """
            
            fingerprint = page.run_js(fingerprint_script)
            return fingerprint or {}
            
        except Exception as e:
            logger.error(f"获取当前指纹失败: {str(e)}")
            return {}
    
    def compare_fingerprints(self, fp1: Dict, fp2: Dict) -> Dict:
        """比较两个指纹的差异
        
        Args:
            fp1: 第一个指纹
            fp2: 第二个指纹
            
        Returns:
            Dict: 比较结果
        """
        try:
            differences = {}
            all_keys = set(fp1.keys()) | set(fp2.keys())
            
            for key in all_keys:
                val1 = fp1.get(key)
                val2 = fp2.get(key)
                
                if val1 != val2:
                    differences[key] = {
                        "fingerprint1": val1,
                        "fingerprint2": val2
                    }
            
            similarity = 1 - (len(differences) / len(all_keys)) if all_keys else 1
            
            return {
                "similarity": similarity,
                "differences": differences,
                "total_keys": len(all_keys),
                "different_keys": len(differences)
            }
            
        except Exception as e:
            logger.error(f"比较指纹失败: {str(e)}")
            return {}
    
    def create_fingerprint_profile(self, name: str, template: str = "windows_chrome", 
                                 custom_settings: Optional[Dict] = None) -> bool:
        """创建指纹配置文件
        
        Args:
            name: 配置文件名称
            template: 基础模板
            custom_settings: 自定义设置
            
        Returns:
            bool: 是否创建成功
        """
        try:
            # 生成基础指纹
            fingerprint = self.generate_fingerprint(template, randomize=True)
            
            # 应用自定义设置
            if custom_settings:
                fingerprint.update(custom_settings)
            
            # 保存指纹
            return self.save_fingerprint(name, fingerprint)
            
        except Exception as e:
            logger.error(f"创建指纹配置文件失败: {str(e)}")
            return False