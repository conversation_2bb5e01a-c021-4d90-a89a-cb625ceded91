"""
高级指纹测试
测试指纹修改在页面加载前的注入效果
"""

import sys
import os
import time
import json
from pathlib import Path

# 添加src目录到Python路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from browser import BrowserManager

def test_advanced_fingerprint():
    """测试高级指纹修改"""
    print("🔍 高级指纹修改测试")
    print("=" * 60)
    
    browser_manager = BrowserManager()
    
    try:
        # 1. 创建浏览器
        print("1. 创建浏览器...")
        page = browser_manager.create_browser(profile_name="advanced_fingerprint_test")
        
        # 2. 在页面加载前注入指纹修改脚本
        print("2. 注入指纹修改脚本...")
        
        fingerprint_injection_script = """
        // 在页面加载前修改navigator属性
        (function() {
            // 保存原始方法
            const originalDefineProperty = Object.defineProperty;
            
            // 自定义指纹数据
            const customFingerprint = {
                userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
                language: 'en-US',
                languages: ['en-US', 'en'],
                platform: 'MacIntel',
                hardwareConcurrency: 8,
                maxTouchPoints: 0
            };
            
            // 重新定义navigator属性
            originalDefineProperty(navigator, 'userAgent', {
                get: function() { return customFingerprint.userAgent; },
                configurable: true
            });
            
            originalDefineProperty(navigator, 'language', {
                get: function() { return customFingerprint.language; },
                configurable: true
            });
            
            originalDefineProperty(navigator, 'languages', {
                get: function() { return customFingerprint.languages; },
                configurable: true
            });
            
            originalDefineProperty(navigator, 'platform', {
                get: function() { return customFingerprint.platform; },
                configurable: true
            });
            
            originalDefineProperty(navigator, 'hardwareConcurrency', {
                get: function() { return customFingerprint.hardwareConcurrency; },
                configurable: true
            });
            
            originalDefineProperty(navigator, 'maxTouchPoints', {
                get: function() { return customFingerprint.maxTouchPoints; },
                configurable: true
            });
            
            // 修改屏幕信息
            originalDefineProperty(screen, 'width', {
                get: function() { return 1440; },
                configurable: true
            });
            
            originalDefineProperty(screen, 'height', {
                get: function() { return 900; },
                configurable: true
            });
            
            console.log('指纹修改脚本已注入');
        })();
        """
        
        # 注入脚本到页面
        page.run_js(fingerprint_injection_script)
        print("   ✅ 指纹修改脚本注入成功")
        
        # 3. 访问测试页面
        print("\n3. 访问测试页面...")
        browser_manager.navigate_to("https://httpbin.org/headers")
        time.sleep(2)
        
        # 4. 检测JavaScript指纹
        print("\n4. 检测JavaScript指纹...")
        
        js_fingerprint = page.run_js("""
            return {
                userAgent: navigator.userAgent,
                language: navigator.language,
                languages: navigator.languages,
                platform: navigator.platform,
                hardwareConcurrency: navigator.hardwareConcurrency,
                maxTouchPoints: navigator.maxTouchPoints,
                screenWidth: screen.width,
                screenHeight: screen.height,
                cookieEnabled: navigator.cookieEnabled,
                doNotTrack: navigator.doNotTrack
            };
        """)
        
        if js_fingerprint:
            print(f"   User-Agent: {js_fingerprint.get('userAgent', 'N/A')}")
            print(f"   语言: {js_fingerprint.get('language', 'N/A')}")
            print(f"   语言列表: {js_fingerprint.get('languages', 'N/A')}")
            print(f"   平台: {js_fingerprint.get('platform', 'N/A')}")
            print(f"   CPU核心数: {js_fingerprint.get('hardwareConcurrency', 'N/A')}")
            print(f"   触摸点数: {js_fingerprint.get('maxTouchPoints', 'N/A')}")
            print(f"   屏幕尺寸: {js_fingerprint.get('screenWidth', 'N/A')}x{js_fingerprint.get('screenHeight', 'N/A')}")
            
            # 检查修改是否成功
            success_count = 0
            total_checks = 0
            
            if 'Chrome/120.0.0.0' in js_fingerprint.get('userAgent', ''):
                success_count += 1
                print("   ✅ User-Agent修改成功")
            else:
                print("   ❌ User-Agent修改失败")
            total_checks += 1
            
            if js_fingerprint.get('language') == 'en-US':
                success_count += 1
                print("   ✅ 语言修改成功")
            else:
                print("   ❌ 语言修改失败")
            total_checks += 1
            
            if js_fingerprint.get('platform') == 'MacIntel':
                success_count += 1
                print("   ✅ 平台修改成功")
            else:
                print("   ❌ 平台修改失败")
            total_checks += 1
            
            if js_fingerprint.get('hardwareConcurrency') == 8:
                success_count += 1
                print("   ✅ CPU核心数修改成功")
            else:
                print("   ❌ CPU核心数修改失败")
            total_checks += 1
            
            if js_fingerprint.get('screenWidth') == 1440:
                success_count += 1
                print("   ✅ 屏幕宽度修改成功")
            else:
                print("   ❌ 屏幕宽度修改失败")
            total_checks += 1
            
            print(f"\n   指纹修改成功率: {success_count}/{total_checks} ({success_count/total_checks*100:.1f}%)")
            
        else:
            print("   ❌ JavaScript指纹检测失败")
        
        # 5. 测试WebRTC指纹
        print("\n5. 测试WebRTC指纹...")
        webrtc_result = page.run_js("""
            return new Promise((resolve) => {
                try {
                    const pc = new RTCPeerConnection({iceServers: []});
                    pc.createDataChannel('');
                    pc.createOffer().then(offer => {
                        pc.setLocalDescription(offer);
                        const lines = offer.sdp.split('\\n');
                        const candidateLine = lines.find(line => line.includes('candidate'));
                        resolve(candidateLine ? 'WebRTC可用' : 'WebRTC不可用');
                    }).catch(() => resolve('WebRTC错误'));
                } catch(e) {
                    resolve('WebRTC不支持');
                }
            });
        """)
        
        if webrtc_result:
            print(f"   WebRTC状态: {webrtc_result}")
        
        # 6. 测试字体指纹
        print("\n6. 测试字体指纹...")
        font_result = page.run_js("""
            const fonts = ['Arial', 'Times New Roman', 'Courier New', 'Helvetica', 'Georgia'];
            const availableFonts = [];
            
            fonts.forEach(font => {
                const canvas = document.createElement('canvas');
                const ctx = canvas.getContext('2d');
                ctx.font = '12px ' + font;
                const width = ctx.measureText('test').width;
                if (width > 0) {
                    availableFonts.push(font);
                }
            });
            
            return availableFonts;
        """)
        
        if font_result:
            print(f"   可用字体: {', '.join(font_result)}")
        
        return True
        
    except Exception as e:
        print(f"❌ 高级指纹测试失败: {str(e)}")
        return False
    finally:
        browser_manager.close_browser()

def test_anti_detection():
    """测试反检测能力"""
    print("\n" + "=" * 60)
    print("🛡️ 测试反检测能力")
    print("=" * 60)
    
    browser_manager = BrowserManager()
    
    try:
        # 创建浏览器
        page = browser_manager.create_browser()
        
        # 访问检测页面
        browser_manager.navigate_to("https://httpbin.org/headers")
        time.sleep(2)
        
        # 检测webdriver属性
        print("1. 检测webdriver属性...")
        webdriver_result = page.run_js("return navigator.webdriver;")
        if webdriver_result is None or webdriver_result == False:
            print("   ✅ webdriver属性已隐藏")
        else:
            print(f"   ❌ webdriver属性暴露: {webdriver_result}")
        
        # 检测chrome属性
        print("\n2. 检测chrome属性...")
        chrome_result = page.run_js("return typeof window.chrome;")
        if chrome_result == "object":
            print("   ✅ chrome属性存在")
        else:
            print(f"   ❌ chrome属性缺失: {chrome_result}")
        
        # 检测plugins
        print("\n3. 检测plugins...")
        plugins_result = page.run_js("return navigator.plugins.length;")
        if plugins_result and plugins_result > 0:
            print(f"   ✅ 检测到 {plugins_result} 个插件")
        else:
            print("   ❌ 没有检测到插件")
        
        # 检测语言设置
        print("\n4. 检测语言设置...")
        languages_result = page.run_js("return navigator.languages;")
        if languages_result and len(languages_result) > 0:
            print(f"   ✅ 语言设置正常: {languages_result}")
        else:
            print("   ❌ 语言设置异常")
        
        return True
        
    except Exception as e:
        print(f"❌ 反检测测试失败: {str(e)}")
        return False
    finally:
        browser_manager.close_browser()

def main():
    """主测试函数"""
    print("🚀 开始高级指纹测试")
    print("测试时间:", time.strftime("%Y-%m-%d %H:%M:%S"))
    
    # 确保必要的目录存在
    Path("profiles").mkdir(exist_ok=True)
    
    test_results = []
    
    # 执行测试
    tests = [
        ("高级指纹修改", test_advanced_fingerprint),
        ("反检测能力", test_anti_detection)
    ]
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            test_results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name}测试异常: {str(e)}")
            test_results.append((test_name, False))
    
    # 输出测试总结
    print("\n" + "=" * 60)
    print("📊 高级测试结果总结")
    print("=" * 60)
    
    passed = 0
    total = len(test_results)
    
    for test_name, result in test_results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n总体结果: {passed}/{total} 项测试通过")

if __name__ == "__main__":
    main()
