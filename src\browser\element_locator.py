"""
元素定位器
负责页面元素的定位和操作
"""

import time
from typing import List, Dict, Optional, Any, Union
from DrissionPage import ChromiumPage, ChromiumElement
import logging

logger = logging.getLogger("browser")


class ElementLocator:
    """元素定位器 - 负责页面元素的定位和操作"""
    
    def __init__(self, page: ChromiumPage):
        """初始化元素定位器"""
        self.page = page
        self.default_timeout = 10
        
    def find_element(self, selector: str, method: str = "css", timeout: int = None) -> Optional[ChromiumElement]:
        """查找单个元素"""
        timeout = timeout or self.default_timeout
        
        try:
            if method == "css":
                element = self.page.ele(selector, timeout=timeout)
            elif method == "xpath":
                element = self.page.ele(f"xpath:{selector}", timeout=timeout)
            elif method == "text":
                element = self.page.ele(f"text:{selector}", timeout=timeout)
            elif method == "tag":
                element = self.page.ele(f"tag:{selector}", timeout=timeout)
            else:
                logger.error(f"不支持的定位方法: {method}")
                return None
            
            if element:
                logger.debug(f"找到元素: {selector}")
                return element
            else:
                logger.warning(f"未找到元素: {selector}")
                return None
                
        except Exception as e:
            logger.error(f"查找元素失败: {selector}, {str(e)}")
            return None
    
    def find_elements(self, selector: str, method: str = "css", timeout: int = None) -> List[ChromiumElement]:
        """查找多个元素"""
        timeout = timeout or self.default_timeout
        
        try:
            if method == "css":
                elements = self.page.eles(selector, timeout=timeout)
            elif method == "xpath":
                elements = self.page.eles(f"xpath:{selector}", timeout=timeout)
            elif method == "text":
                elements = self.page.eles(f"text:{selector}", timeout=timeout)
            elif method == "tag":
                elements = self.page.eles(f"tag:{selector}", timeout=timeout)
            else:
                logger.error(f"不支持的定位方法: {method}")
                return []
            
            logger.debug(f"找到 {len(elements)} 个元素: {selector}")
            return elements
            
        except Exception as e:
            logger.error(f"查找元素列表失败: {selector}, {str(e)}")
            return []
    
    def wait_for_element(self, selector: str, method: str = "css", timeout: int = None) -> Optional[ChromiumElement]:
        """等待元素出现"""
        timeout = timeout or self.default_timeout
        
        try:
            if method == "css":
                element = self.page.wait.ele_loaded(selector, timeout)
            elif method == "xpath":
                element = self.page.wait.ele_loaded(f"xpath:{selector}", timeout)
            elif method == "text":
                element = self.page.wait.ele_loaded(f"text:{selector}", timeout)
            else:
                element = self.page.wait.ele_loaded(selector, timeout)
            
            if element:
                logger.debug(f"等待元素成功: {selector}")
                return element
            else:
                logger.warning(f"等待元素超时: {selector}")
                return None
                
        except Exception as e:
            logger.error(f"等待元素失败: {selector}, {str(e)}")
            return None
    
    def click_element(self, element: Union[ChromiumElement, str], wait_time: float = 1.0) -> bool:
        """点击元素"""
        try:
            if isinstance(element, str):
                element = self.find_element(element)
                if not element:
                    return False
            
            element.click()
            
            if wait_time > 0:
                time.sleep(wait_time)
            
            logger.debug("元素点击成功")
            return True
            
        except Exception as e:
            logger.error(f"点击元素失败: {str(e)}")
            return False
    
    def input_text(self, element: Union[ChromiumElement, str], text: str, clear_first: bool = True) -> bool:
        """输入文本"""
        try:
            if isinstance(element, str):
                element = self.find_element(element)
                if not element:
                    return False
            
            if clear_first:
                element.clear()
            
            element.input(text)
            logger.debug(f"文本输入成功: {text[:20]}...")
            return True
            
        except Exception as e:
            logger.error(f"输入文本失败: {str(e)}")
            return False
    
    def get_element_text(self, element: Union[ChromiumElement, str]) -> str:
        """获取元素文本"""
        try:
            if isinstance(element, str):
                element = self.find_element(element)
                if not element:
                    return ""
            
            text = element.text
            logger.debug(f"获取元素文本成功: {text[:50]}...")
            return text
            
        except Exception as e:
            logger.error(f"获取元素文本失败: {str(e)}")
            return ""
    
    def get_element_attribute(self, element: Union[ChromiumElement, str], attribute: str) -> str:
        """获取元素属性"""
        try:
            if isinstance(element, str):
                element = self.find_element(element)
                if not element:
                    return ""
            
            attr_value = element.attr(attribute)
            logger.debug(f"获取元素属性成功: {attribute}={attr_value}")
            return attr_value or ""
            
        except Exception as e:
            logger.error(f"获取元素属性失败: {str(e)}")
            return ""
    
    def is_element_visible(self, element: Union[ChromiumElement, str]) -> bool:
        """检查元素是否可见"""
        try:
            if isinstance(element, str):
                element = self.find_element(element)
                if not element:
                    return False
            
            return element.states.is_displayed
            
        except Exception as e:
            logger.error(f"检查元素可见性失败: {str(e)}")
            return False
    
    def is_element_enabled(self, element: Union[ChromiumElement, str]) -> bool:
        """检查元素是否可用"""
        try:
            if isinstance(element, str):
                element = self.find_element(element)
                if not element:
                    return False
            
            return element.states.is_enabled
            
        except Exception as e:
            logger.error(f"检查元素可用性失败: {str(e)}")
            return False
    
    def hover_element(self, element: Union[ChromiumElement, str]) -> bool:
        """悬停在元素上"""
        try:
            if isinstance(element, str):
                element = self.find_element(element)
                if not element:
                    return False
            
            element.hover()
            logger.debug("元素悬停成功")
            return True
            
        except Exception as e:
            logger.error(f"元素悬停失败: {str(e)}")
            return False
    
    def select_option(self, element: Union[ChromiumElement, str], option_value: str, by: str = "value") -> bool:
        """选择下拉框选项"""
        try:
            if isinstance(element, str):
                element = self.find_element(element)
                if not element:
                    return False
            
            if by == "value":
                element.select(option_value)
            elif by == "text":
                element.select.by_text(option_value)
            elif by == "index":
                element.select.by_index(int(option_value))
            else:
                logger.error(f"不支持的选择方式: {by}")
                return False
            
            logger.debug(f"选择选项成功: {option_value}")
            return True
            
        except Exception as e:
            logger.error(f"选择选项失败: {str(e)}")
            return False
    
    def drag_and_drop(self, source: Union[ChromiumElement, str], target: Union[ChromiumElement, str]) -> bool:
        """拖拽元素"""
        try:
            if isinstance(source, str):
                source = self.find_element(source)
                if not source:
                    return False
            
            if isinstance(target, str):
                target = self.find_element(target)
                if not target:
                    return False
            
            source.drag_to(target)
            logger.debug("拖拽操作成功")
            return True
            
        except Exception as e:
            logger.error(f"拖拽操作失败: {str(e)}")
            return False
    
    def scroll_to_element(self, element: Union[ChromiumElement, str]) -> bool:
        """滚动到元素"""
        try:
            if isinstance(element, str):
                element = self.find_element(element)
                if not element:
                    return False
            
            element.scroll.to_see()
            logger.debug("滚动到元素成功")
            return True
            
        except Exception as e:
            logger.error(f"滚动到元素失败: {str(e)}")
            return False
    
    def get_element_location(self, element: Union[ChromiumElement, str]) -> Dict[str, int]:
        """获取元素位置"""
        try:
            if isinstance(element, str):
                element = self.find_element(element)
                if not element:
                    return {}
            
            rect = element.rect
            return {
                "x": rect.x,
                "y": rect.y,
                "width": rect.width,
                "height": rect.height
            }
            
        except Exception as e:
            logger.error(f"获取元素位置失败: {str(e)}")
            return {}
    
    def get_element_size(self, element: Union[ChromiumElement, str]) -> Dict[str, int]:
        """获取元素大小"""
        try:
            if isinstance(element, str):
                element = self.find_element(element)
                if not element:
                    return {}
            
            rect = element.rect
            return {
                "width": rect.width,
                "height": rect.height
            }
            
        except Exception as e:
            logger.error(f"获取元素大小失败: {str(e)}")
            return {}
    
    def wait_for_element_clickable(self, selector: str, timeout: int = None) -> Optional[ChromiumElement]:
        """等待元素可点击"""
        timeout = timeout or self.default_timeout
        
        try:
            element = self.wait_for_element(selector, timeout=timeout)
            if element and self.is_element_enabled(element):
                return element
            return None
            
        except Exception as e:
            logger.error(f"等待元素可点击失败: {str(e)}")
            return None
    
    def get_elements_text(self, elements: List[ChromiumElement]) -> List[str]:
        """获取多个元素的文本"""
        texts = []
        for element in elements:
            try:
                text = element.text
                texts.append(text)
            except Exception as e:
                logger.warning(f"获取元素文本失败: {str(e)}")
                texts.append("")
        
        return texts
    
    def batch_click_elements(self, selectors: List[str], wait_time: float = 1.0) -> List[bool]:
        """批量点击元素"""
        results = []
        for selector in selectors:
            result = self.click_element(selector, wait_time)
            results.append(result)
        
        return results
    
    def extract_table_data(self, table_selector: str) -> List[List[str]]:
        """提取表格数据"""
        try:
            table = self.find_element(table_selector)
            if not table:
                return []
            
            rows = self.find_elements("tr", method="css")
            table_data = []
            
            for row in rows:
                cells = row.eles("td") or row.eles("th")
                row_data = [cell.text for cell in cells]
                if row_data:
                    table_data.append(row_data)
            
            logger.debug(f"提取表格数据成功: {len(table_data)} 行")
            return table_data
            
        except Exception as e:
            logger.error(f"提取表格数据失败: {str(e)}")
            return []
    
    def get_all_links(self, base_url: str = "") -> List[Dict[str, str]]:
        """获取页面所有链接"""
        try:
            links = self.find_elements("a", method="tag")
            link_data = []
            
            for link in links:
                href = link.attr("href")
                text = link.text
                
                if href:
                    if base_url and not href.startswith("http"):
                        href = base_url.rstrip("/") + "/" + href.lstrip("/")
                    
                    link_data.append({
                        "url": href,
                        "text": text,
                        "title": link.attr("title") or ""
                    })
            
            logger.debug(f"获取链接成功: {len(link_data)} 个")
            return link_data
            
        except Exception as e:
            logger.error(f"获取链接失败: {str(e)}")
            return []
    
    def check_element_exists(self, selector: str, method: str = "css") -> bool:
        """检查元素是否存在"""
        element = self.find_element(selector, method, timeout=1)
        return element is not None
    
    def get_element_info(self, element: Union[ChromiumElement, str]) -> Dict:
        """获取元素详细信息"""
        try:
            if isinstance(element, str):
                element = self.find_element(element)
                if not element:
                    return {}
            
            return {
                "tag": element.tag,
                "text": element.text,
                "location": self.get_element_location(element),
                "size": self.get_element_size(element),
                "visible": self.is_element_visible(element),
                "enabled": self.is_element_enabled(element),
                "attributes": {
                    "id": element.attr("id") or "",
                    "class": element.attr("class") or "",
                    "href": element.attr("href") or "",
                    "src": element.attr("src") or ""
                }
            }
            
        except Exception as e:
            logger.error(f"获取元素信息失败: {str(e)}")
            return {}