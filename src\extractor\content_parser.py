"""
内容解析器
负责HTML内容的解析和提取
"""

import re
from typing import List, Dict, Optional
from bs4 import BeautifulSoup, Tag
from urllib.parse import urljoin, urlparse
import logging

logger = logging.getLogger("extractor")


class ContentParser:
    """内容解析器 - 负责HTML内容的解析和提取"""
    
    def __init__(self):
        """初始化内容解析器"""
        self.soup = None
        self.base_url = ""
        
        logger.info("内容解析器初始化完成")
    
    def parse_html(self, html: str, base_url: str = "") -> BeautifulSoup:
        """解析HTML内容
        
        Args:
            html: HTML字符串
            base_url: 基础URL，用于处理相对链接
            
        Returns:
            BeautifulSoup: 解析后的对象
        """
        try:
            self.soup = BeautifulSoup(html, 'html.parser')
            self.base_url = base_url
            
            logger.debug("HTML解析成功")
            return self.soup
            
        except Exception as e:
            logger.error(f"HTML解析失败: {str(e)}")
            return BeautifulSoup("", 'html.parser')
    
    def extract_text(self, html: str = None, selector: str = None, clean: bool = True) -> str:
        """提取文本内容
        
        Args:
            html: HTML字符串，为None时使用已解析的soup
            selector: CSS选择器，为None时提取全部文本
            clean: 是否清理文本
            
        Returns:
            str: 提取的文本内容
        """
        try:
            if html:
                soup = BeautifulSoup(html, 'html.parser')
            else:
                soup = self.soup
            
            if not soup:
                return ""
            
            if selector:
                elements = soup.select(selector)
                text = " ".join([elem.get_text() for elem in elements])
            else:
                text = soup.get_text()
            
            if clean:
                text = self.clean_text(text)
            
            logger.debug(f"文本提取成功，长度: {len(text)}")
            return text
            
        except Exception as e:
            logger.error(f"文本提取失败: {str(e)}")
            return ""
    
    def extract_links(self, html: str = None, base_url: str = None) -> List[Dict[str, str]]:
        """提取链接
        
        Args:
            html: HTML字符串
            base_url: 基础URL
            
        Returns:
            List[Dict]: 链接列表
        """
        try:
            if html:
                soup = BeautifulSoup(html, 'html.parser')
            else:
                soup = self.soup
            
            if not soup:
                return []
            
            base_url = base_url or self.base_url
            links = []
            
            for link in soup.find_all('a', href=True):
                href = link['href']
                text = link.get_text(strip=True)
                title = link.get('title', '')
                
                # 处理相对链接
                if base_url and not href.startswith(('http://', 'https://', '//')):
                    href = urljoin(base_url, href)
                
                links.append({
                    'url': href,
                    'text': text,
                    'title': title
                })
            
            logger.debug(f"链接提取成功: {len(links)} 个")
            return links
            
        except Exception as e:
            logger.error(f"链接提取失败: {str(e)}")
            return []
    
    def extract_images(self, html: str = None, base_url: str = None) -> List[Dict[str, str]]:
        """提取图片链接
        
        Args:
            html: HTML字符串
            base_url: 基础URL
            
        Returns:
            List[Dict]: 图片信息列表
        """
        try:
            if html:
                soup = BeautifulSoup(html, 'html.parser')
            else:
                soup = self.soup
            
            if not soup:
                return []
            
            base_url = base_url or self.base_url
            images = []
            
            for img in soup.find_all('img'):
                src = img.get('src', '')
                alt = img.get('alt', '')
                title = img.get('title', '')
                
                if src:
                    # 处理相对链接
                    if base_url and not src.startswith(('http://', 'https://', '//', 'data:')):
                        src = urljoin(base_url, src)
                    
                    images.append({
                        'src': src,
                        'alt': alt,
                        'title': title
                    })
            
            logger.debug(f"图片提取成功: {len(images)} 个")
            return images
            
        except Exception as e:
            logger.error(f"图片提取失败: {str(e)}")
            return []
    
    def clean_text(self, text: str) -> str:
        """清理文本内容
        
        Args:
            text: 原始文本
            
        Returns:
            str: 清理后的文本
        """
        try:
            if not text:
                return ""
            
            # 移除多余的空白字符
            text = re.sub(r'\s+', ' ', text)
            
            # 移除首尾空白
            text = text.strip()
            
            # 移除特殊字符（可选）
            # text = re.sub(r'[^\w\s\u4e00-\u9fff]', '', text)
            
            return text
            
        except Exception as e:
            logger.error(f"文本清理失败: {str(e)}")
            return text
    
    def extract_table_data(self, html: str = None, table_selector: str = "table") -> List[List[str]]:
        """提取表格数据
        
        Args:
            html: HTML字符串
            table_selector: 表格选择器
            
        Returns:
            List[List[str]]: 表格数据
        """
        try:
            if html:
                soup = BeautifulSoup(html, 'html.parser')
            else:
                soup = self.soup
            
            if not soup:
                return []
            
            table_data = []
            tables = soup.select(table_selector)
            
            for table in tables:
                rows = table.find_all('tr')
                for row in rows:
                    cells = row.find_all(['td', 'th'])
                    row_data = [cell.get_text(strip=True) for cell in cells]
                    if row_data:
                        table_data.append(row_data)
            
            logger.debug(f"表格数据提取成功: {len(table_data)} 行")
            return table_data
            
        except Exception as e:
            logger.error(f"表格数据提取失败: {str(e)}")
            return []
    
    def extract_meta_data(self, html: str = None) -> Dict[str, str]:
        """提取页面元数据
        
        Args:
            html: HTML字符串
            
        Returns:
            Dict: 元数据字典
        """
        try:
            if html:
                soup = BeautifulSoup(html, 'html.parser')
            else:
                soup = self.soup
            
            if not soup:
                return {}
            
            meta_data = {}
            
            # 提取title
            title_tag = soup.find('title')
            if title_tag:
                meta_data['title'] = title_tag.get_text(strip=True)
            
            # 提取meta标签
            meta_tags = soup.find_all('meta')
            for meta in meta_tags:
                name = meta.get('name') or meta.get('property')
                content = meta.get('content')
                
                if name and content:
                    meta_data[name] = content
            
            logger.debug(f"元数据提取成功: {len(meta_data)} 项")
            return meta_data
            
        except Exception as e:
            logger.error(f"元数据提取失败: {str(e)}")
            return {}
    
    def extract_by_xpath(self, xpath: str, html: str = None) -> List[str]:
        """通过XPath提取内容
        
        Args:
            xpath: XPath表达式
            html: HTML字符串
            
        Returns:
            List[str]: 提取的内容列表
        """
        try:
            # 注意：BeautifulSoup不直接支持XPath，这里提供CSS选择器的替代方案
            logger.warning("BeautifulSoup不支持XPath，请使用CSS选择器")
            return []
            
        except Exception as e:
            logger.error(f"XPath提取失败: {str(e)}")
            return []
    
    def extract_structured_data(self, html: str = None, schema_type: str = "Article") -> List[Dict]:
        """提取结构化数据（JSON-LD）
        
        Args:
            html: HTML字符串
            schema_type: Schema类型
            
        Returns:
            List[Dict]: 结构化数据列表
        """
        try:
            if html:
                soup = BeautifulSoup(html, 'html.parser')
            else:
                soup = self.soup
            
            if not soup:
                return []
            
            structured_data = []
            
            # 查找JSON-LD脚本
            scripts = soup.find_all('script', type='application/ld+json')
            
            for script in scripts:
                try:
                    import json
                    data = json.loads(script.string)
                    
                    # 过滤指定类型的数据
                    if isinstance(data, dict):
                        if data.get('@type') == schema_type:
                            structured_data.append(data)
                    elif isinstance(data, list):
                        for item in data:
                            if isinstance(item, dict) and item.get('@type') == schema_type:
                                structured_data.append(item)
                                
                except json.JSONDecodeError:
                    continue
            
            logger.debug(f"结构化数据提取成功: {len(structured_data)} 项")
            return structured_data
            
        except Exception as e:
            logger.error(f"结构化数据提取失败: {str(e)}")
            return []
    
    def extract_form_data(self, html: str = None, form_selector: str = "form") -> List[Dict]:
        """提取表单数据
        
        Args:
            html: HTML字符串
            form_selector: 表单选择器
            
        Returns:
            List[Dict]: 表单信息列表
        """
        try:
            if html:
                soup = BeautifulSoup(html, 'html.parser')
            else:
                soup = self.soup
            
            if not soup:
                return []
            
            forms_data = []
            forms = soup.select(form_selector)
            
            for form in forms:
                form_info = {
                    'action': form.get('action', ''),
                    'method': form.get('method', 'GET').upper(),
                    'fields': []
                }
                
                # 提取输入字段
                inputs = form.find_all(['input', 'textarea', 'select'])
                for input_elem in inputs:
                    field_info = {
                        'name': input_elem.get('name', ''),
                        'type': input_elem.get('type', 'text'),
                        'value': input_elem.get('value', ''),
                        'required': input_elem.has_attr('required')
                    }
                    form_info['fields'].append(field_info)
                
                forms_data.append(form_info)
            
            logger.debug(f"表单数据提取成功: {len(forms_data)} 个表单")
            return forms_data
            
        except Exception as e:
            logger.error(f"表单数据提取失败: {str(e)}")
            return []
    
    def extract_custom_data(self, extraction_rules: Dict, html: str = None) -> Dict:
        """根据自定义规则提取数据
        
        Args:
            html: HTML字符串
            extraction_rules: 提取规则字典
                格式: {
                    'field_name': {
                        'selector': 'css_selector',
                        'attribute': 'text|href|src|...',
                        'multiple': True|False,
                        'regex': 'regex_pattern'
                    }
                }
            
        Returns:
            Dict: 提取的数据
        """
        try:
            if html:
                soup = BeautifulSoup(html, 'html.parser')
            else:
                soup = self.soup
            
            if not soup:
                return {}
            
            extracted_data = {}
            
            for field_name, rule in extraction_rules.items():
                selector = rule.get('selector', '')
                attribute = rule.get('attribute', 'text')
                multiple = rule.get('multiple', False)
                regex_pattern = rule.get('regex', '')
                
                if not selector:
                    continue
                
                elements = soup.select(selector)
                
                if not elements:
                    extracted_data[field_name] = [] if multiple else ""
                    continue
                
                values = []
                for elem in elements:
                    if attribute == 'text':
                        value = elem.get_text(strip=True)
                    else:
                        value = elem.get(attribute, '')
                    
                    # 应用正则表达式
                    if regex_pattern and value:
                        match = re.search(regex_pattern, value)
                        value = match.group(1) if match and match.groups() else value
                    
                    values.append(value)
                
                if multiple:
                    extracted_data[field_name] = values
                else:
                    extracted_data[field_name] = values[0] if values else ""
            
            logger.debug(f"自定义数据提取成功: {len(extracted_data)} 个字段")
            return extracted_data
            
        except Exception as e:
            logger.error(f"自定义数据提取失败: {str(e)}")
            return {}
    
    def get_page_statistics(self, html: str = None) -> Dict:
        """获取页面统计信息
        
        Args:
            html: HTML字符串
            
        Returns:
            Dict: 统计信息
        """
        try:
            if html:
                soup = BeautifulSoup(html, 'html.parser')
            else:
                soup = self.soup
            
            if not soup:
                return {}
            
            stats = {
                'total_elements': len(soup.find_all()),
                'links_count': len(soup.find_all('a')),
                'images_count': len(soup.find_all('img')),
                'forms_count': len(soup.find_all('form')),
                'tables_count': len(soup.find_all('table')),
                'scripts_count': len(soup.find_all('script')),
                'styles_count': len(soup.find_all('style')),
                'text_length': len(soup.get_text()),
                'html_size': len(str(soup))
            }
            
            return stats
            
        except Exception as e:
            logger.error(f"页面统计失败: {str(e)}")
            return {}