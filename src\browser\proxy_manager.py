"""
代理管理器
管理代理服务器配置和轮换
"""

import json
import random
import requests
from pathlib import Path
from typing import Dict, List, Optional, Tuple
import logging

logger = logging.getLogger("browser")


class ProxyManager:
    """代理管理器 - 管理代理服务器配置和轮换"""
    
    def __init__(self, proxy_file: str = "./config/proxies.json"):
        """初始化代理管理器
        
        Args:
            proxy_file: 代理配置文件路径
        """
        self.proxy_file = Path(proxy_file)
        self.proxy_file.parent.mkdir(parents=True, exist_ok=True)
        
        self.proxies = self._load_proxies()
        self.current_proxy_index = 0
        
        logger.info(f"代理管理器初始化完成，加载 {len(self.proxies)} 个代理")
    
    def _load_proxies(self) -> List[Dict]:
        """加载代理配置"""
        try:
            if self.proxy_file.exists():
                with open(self.proxy_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            else:
                # 创建默认配置文件
                default_proxies = []
                self._save_proxies(default_proxies)
                return default_proxies
        except Exception as e:
            logger.error(f"加载代理配置失败: {str(e)}")
            return []
    
    def _save_proxies(self, proxies: List[Dict]) -> bool:
        """保存代理配置"""
        try:
            with open(self.proxy_file, 'w', encoding='utf-8') as f:
                json.dump(proxies, f, ensure_ascii=False, indent=2)
            return True
        except Exception as e:
            logger.error(f"保存代理配置失败: {str(e)}")
            return False
    
    def add_proxy(self, host: str, port: int, username: str = "", password: str = "", 
                  proxy_type: str = "http", name: str = "") -> bool:
        """添加代理服务器
        
        Args:
            host: 代理主机
            port: 代理端口
            username: 用户名
            password: 密码
            proxy_type: 代理类型 (http/https/socks5)
            name: 代理名称
            
        Returns:
            bool: 是否添加成功
        """
        try:
            proxy_config = {
                "name": name or f"{host}:{port}",
                "host": host,
                "port": port,
                "username": username,
                "password": password,
                "type": proxy_type,
                "enabled": True,
                "last_used": None,
                "success_count": 0,
                "fail_count": 0
            }
            
            self.proxies.append(proxy_config)
            self._save_proxies(self.proxies)
            
            logger.info(f"代理添加成功: {host}:{port}")
            return True
            
        except Exception as e:
            logger.error(f"添加代理失败: {str(e)}")
            return False
    
    def remove_proxy(self, index: int) -> bool:
        """移除代理服务器
        
        Args:
            index: 代理索引
            
        Returns:
            bool: 是否移除成功
        """
        try:
            if 0 <= index < len(self.proxies):
                removed_proxy = self.proxies.pop(index)
                self._save_proxies(self.proxies)
                
                logger.info(f"代理移除成功: {removed_proxy['name']}")
                return True
            else:
                logger.error(f"无效的代理索引: {index}")
                return False
                
        except Exception as e:
            logger.error(f"移除代理失败: {str(e)}")
            return False
    
    def get_next_proxy(self) -> Optional[Dict]:
        """获取下一个可用代理
        
        Returns:
            Dict: 代理配置，无可用代理返回None
        """
        try:
            enabled_proxies = [p for p in self.proxies if p.get('enabled', True)]
            
            if not enabled_proxies:
                logger.warning("没有可用的代理服务器")
                return None
            
            # 轮换到下一个代理
            if self.current_proxy_index >= len(enabled_proxies):
                self.current_proxy_index = 0
            
            proxy = enabled_proxies[self.current_proxy_index]
            self.current_proxy_index += 1
            
            logger.debug(f"获取代理: {proxy['name']}")
            return proxy
            
        except Exception as e:
            logger.error(f"获取代理失败: {str(e)}")
            return None
    
    def get_random_proxy(self) -> Optional[Dict]:
        """获取随机代理
        
        Returns:
            Dict: 代理配置
        """
        try:
            enabled_proxies = [p for p in self.proxies if p.get('enabled', True)]
            
            if not enabled_proxies:
                return None
            
            proxy = random.choice(enabled_proxies)
            logger.debug(f"获取随机代理: {proxy['name']}")
            return proxy
            
        except Exception as e:
            logger.error(f"获取随机代理失败: {str(e)}")
            return None
    
    def test_proxy(self, proxy: Dict, test_url: str = "http://httpbin.org/ip", timeout: int = 10) -> bool:
        """测试代理连接
        
        Args:
            proxy: 代理配置
            test_url: 测试URL
            timeout: 超时时间
            
        Returns:
            bool: 代理是否可用
        """
        try:
            # 构建代理URL
            proxy_url = self._build_proxy_url(proxy)
            if not proxy_url:
                return False
            
            # 设置代理
            proxies = {
                'http': proxy_url,
                'https': proxy_url
            }
            
            # 发送测试请求
            response = requests.get(test_url, proxies=proxies, timeout=timeout)
            
            if response.status_code == 200:
                # 更新成功计数
                proxy['success_count'] = proxy.get('success_count', 0) + 1
                proxy['last_used'] = self._get_current_time()
                
                logger.info(f"代理测试成功: {proxy['name']}")
                return True
            else:
                # 更新失败计数
                proxy['fail_count'] = proxy.get('fail_count', 0) + 1
                logger.warning(f"代理测试失败: {proxy['name']}, 状态码: {response.status_code}")
                return False
                
        except Exception as e:
            # 更新失败计数
            proxy['fail_count'] = proxy.get('fail_count', 0) + 1
            logger.error(f"代理测试异常: {proxy['name']}, {str(e)}")
            return False
    
    def _build_proxy_url(self, proxy: Dict) -> str:
        """构建代理URL
        
        Args:
            proxy: 代理配置
            
        Returns:
            str: 代理URL
        """
        try:
            host = proxy['host']
            port = proxy['port']
            username = proxy.get('username', '')
            password = proxy.get('password', '')
            proxy_type = proxy.get('type', 'http')
            
            if username and password:
                return f"{proxy_type}://{username}:{password}@{host}:{port}"
            else:
                return f"{proxy_type}://{host}:{port}"
                
        except Exception as e:
            logger.error(f"构建代理URL失败: {str(e)}")
            return ""
    
    def test_all_proxies(self, test_url: str = "http://httpbin.org/ip", timeout: int = 10) -> Dict:
        """测试所有代理
        
        Args:
            test_url: 测试URL
            timeout: 超时时间
            
        Returns:
            Dict: 测试结果统计
        """
        try:
            total_count = len(self.proxies)
            success_count = 0
            fail_count = 0
            
            for proxy in self.proxies:
                if self.test_proxy(proxy, test_url, timeout):
                    success_count += 1
                else:
                    fail_count += 1
            
            # 保存更新后的代理配置
            self._save_proxies(self.proxies)
            
            result = {
                "total": total_count,
                "success": success_count,
                "fail": fail_count,
                "success_rate": success_count / total_count if total_count > 0 else 0
            }
            
            logger.info(f"代理测试完成: {success_count}/{total_count} 可用")
            return result
            
        except Exception as e:
            logger.error(f"测试所有代理失败: {str(e)}")
            return {"total": 0, "success": 0, "fail": 0, "success_rate": 0}
    
    def get_best_proxy(self) -> Optional[Dict]:
        """获取最佳代理（成功率最高）
        
        Returns:
            Dict: 最佳代理配置
        """
        try:
            enabled_proxies = [p for p in self.proxies if p.get('enabled', True)]
            
            if not enabled_proxies:
                return None
            
            # 按成功率排序
            def get_success_rate(proxy):
                success = proxy.get('success_count', 0)
                fail = proxy.get('fail_count', 0)
                total = success + fail
                return success / total if total > 0 else 0
            
            best_proxy = max(enabled_proxies, key=get_success_rate)
            logger.debug(f"获取最佳代理: {best_proxy['name']}")
            return best_proxy
            
        except Exception as e:
            logger.error(f"获取最佳代理失败: {str(e)}")
            return None
    
    def disable_proxy(self, index: int) -> bool:
        """禁用代理
        
        Args:
            index: 代理索引
            
        Returns:
            bool: 是否成功
        """
        try:
            if 0 <= index < len(self.proxies):
                self.proxies[index]['enabled'] = False
                self._save_proxies(self.proxies)
                
                logger.info(f"代理已禁用: {self.proxies[index]['name']}")
                return True
            else:
                logger.error(f"无效的代理索引: {index}")
                return False
                
        except Exception as e:
            logger.error(f"禁用代理失败: {str(e)}")
            return False
    
    def enable_proxy(self, index: int) -> bool:
        """启用代理
        
        Args:
            index: 代理索引
            
        Returns:
            bool: 是否成功
        """
        try:
            if 0 <= index < len(self.proxies):
                self.proxies[index]['enabled'] = True
                self._save_proxies(self.proxies)
                
                logger.info(f"代理已启用: {self.proxies[index]['name']}")
                return True
            else:
                logger.error(f"无效的代理索引: {index}")
                return False
                
        except Exception as e:
            logger.error(f"启用代理失败: {str(e)}")
            return False
    
    def get_proxy_stats(self) -> Dict:
        """获取代理统计信息
        
        Returns:
            Dict: 统计信息
        """
        try:
            total_proxies = len(self.proxies)
            enabled_proxies = len([p for p in self.proxies if p.get('enabled', True)])
            disabled_proxies = total_proxies - enabled_proxies
            
            total_success = sum(p.get('success_count', 0) for p in self.proxies)
            total_fail = sum(p.get('fail_count', 0) for p in self.proxies)
            total_requests = total_success + total_fail
            
            overall_success_rate = total_success / total_requests if total_requests > 0 else 0
            
            return {
                "total_proxies": total_proxies,
                "enabled_proxies": enabled_proxies,
                "disabled_proxies": disabled_proxies,
                "total_requests": total_requests,
                "total_success": total_success,
                "total_fail": total_fail,
                "overall_success_rate": overall_success_rate
            }
            
        except Exception as e:
            logger.error(f"获取代理统计失败: {str(e)}")
            return {}
    
    def list_proxies(self) -> List[Dict]:
        """列出所有代理
        
        Returns:
            List[Dict]: 代理列表
        """
        return self.proxies.copy()
    
    def clear_proxy_stats(self) -> bool:
        """清除代理统计信息
        
        Returns:
            bool: 是否成功
        """
        try:
            for proxy in self.proxies:
                proxy['success_count'] = 0
                proxy['fail_count'] = 0
                proxy['last_used'] = None
            
            self._save_proxies(self.proxies)
            logger.info("代理统计信息已清除")
            return True
            
        except Exception as e:
            logger.error(f"清除代理统计失败: {str(e)}")
            return False
    
    def import_proxies_from_file(self, file_path: str, format: str = "txt") -> int:
        """从文件导入代理
        
        Args:
            file_path: 文件路径
            format: 文件格式 (txt/json)
            
        Returns:
            int: 导入的代理数量
        """
        try:
            file_path = Path(file_path)
            if not file_path.exists():
                logger.error(f"文件不存在: {file_path}")
                return 0
            
            imported_count = 0
            
            if format == "txt":
                # 文本格式: host:port 或 host:port:username:password
                with open(file_path, 'r', encoding='utf-8') as f:
                    for line in f:
                        line = line.strip()
                        if not line or line.startswith('#'):
                            continue
                        
                        parts = line.split(':')
                        if len(parts) >= 2:
                            host = parts[0]
                            port = int(parts[1])
                            username = parts[2] if len(parts) > 2 else ""
                            password = parts[3] if len(parts) > 3 else ""
                            
                            if self.add_proxy(host, port, username, password):
                                imported_count += 1
            
            elif format == "json":
                # JSON格式
                with open(file_path, 'r', encoding='utf-8') as f:
                    proxy_data = json.load(f)
                    
                    if isinstance(proxy_data, list):
                        for proxy in proxy_data:
                            if isinstance(proxy, dict) and 'host' in proxy and 'port' in proxy:
                                if self.add_proxy(
                                    proxy['host'],
                                    proxy['port'],
                                    proxy.get('username', ''),
                                    proxy.get('password', ''),
                                    proxy.get('type', 'http'),
                                    proxy.get('name', '')
                                ):
                                    imported_count += 1
            
            logger.info(f"代理导入完成: {imported_count} 个")
            return imported_count
            
        except Exception as e:
            logger.error(f"导入代理失败: {str(e)}")
            return 0
    
    def export_proxies_to_file(self, file_path: str, format: str = "json") -> bool:
        """导出代理到文件
        
        Args:
            file_path: 文件路径
            format: 文件格式 (txt/json)
            
        Returns:
            bool: 是否成功
        """
        try:
            file_path = Path(file_path)
            file_path.parent.mkdir(parents=True, exist_ok=True)
            
            if format == "txt":
                # 文本格式
                with open(file_path, 'w', encoding='utf-8') as f:
                    for proxy in self.proxies:
                        if proxy.get('username') and proxy.get('password'):
                            line = f"{proxy['host']}:{proxy['port']}:{proxy['username']}:{proxy['password']}\n"
                        else:
                            line = f"{proxy['host']}:{proxy['port']}\n"
                        f.write(line)
            
            elif format == "json":
                # JSON格式
                with open(file_path, 'w', encoding='utf-8') as f:
                    json.dump(self.proxies, f, ensure_ascii=False, indent=2)
            
            logger.info(f"代理导出成功: {file_path}")
            return True
            
        except Exception as e:
            logger.error(f"导出代理失败: {str(e)}")
            return False
    
    def _get_current_time(self) -> str:
        """获取当前时间字符串"""
        from datetime import datetime
        return datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    
    def get_proxy_for_browser(self, proxy: Dict) -> str:
        """获取浏览器代理配置字符串
        
        Args:
            proxy: 代理配置
            
        Returns:
            str: 浏览器代理字符串
        """
        try:
            return self._build_proxy_url(proxy)
        except Exception as e:
            logger.error(f"获取浏览器代理配置失败: {str(e)}")
            return ""
    
    def auto_rotate_proxy(self, interval_minutes: int = 30) -> bool:
        """自动轮换代理（需要配合定时任务使用）
        
        Args:
            interval_minutes: 轮换间隔（分钟）
            
        Returns:
            bool: 是否成功
        """
        try:
            # 这里只是标记需要轮换，实际轮换逻辑需要在调用方实现
            logger.info(f"设置代理自动轮换间隔: {interval_minutes} 分钟")
            return True
        except Exception as e:
            logger.error(f"设置代理自动轮换失败: {str(e)}")
            return False
