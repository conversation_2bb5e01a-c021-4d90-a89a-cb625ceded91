"""
浏览器操作分类模块
提供完整的浏览器操作接口，基于DrissionPage框架
支持指纹浏览器环境保存和恢复功能
"""

from .browser_manager import BrowserManager
from .page_controller import PageController
from .element_locator import ElementLocator
from .screenshot_handler import ScreenshotHandler
from .fingerprint_manager import FingerprintManager
from .proxy_manager import ProxyManager

__all__ = [
    'BrowserManager',
    'PageController', 
    'ElementLocator',
    'ScreenshotHandler',
    'FingerprintManager',
    'ProxyManager'
]

__version__ = "4.0.0"
__author__ = "起点爬虫团队"


class BrowserInterface:
    """浏览器操作统一接口"""
    
    def __init__(self, config=None):
        """初始化浏览器接口"""
        self.browser_manager = BrowserManager(config)
        self.page_controller = None
        self.element_locator = None
        self.screenshot_handler = None
        self.fingerprint_manager = FingerprintManager()
        self.proxy_manager = ProxyManager()
        
    def init_browser(self, config: dict = None) -> bool:
        """初始化浏览器"""
        try:
            page = self.browser_manager.create_browser(
                profile_name=config.get('profile_name') if config else None,
                headless=config.get('headless') if config else None
            )
            
            if page:
                self.page_controller = PageController(page)
                self.element_locator = ElementLocator(page)
                self.screenshot_handler = ScreenshotHandler(page)
                return True
            return False
        except Exception:
            return False
    
    def close_browser(self) -> bool:
        """关闭浏览器"""
        try:
            self.browser_manager.close_browser()
            return True
        except Exception:
            return False
    
    def navigate(self, url: str) -> dict:
        """导航到URL"""
        if self.page_controller:
            return self.page_controller.navigate_to(url)
        return {"success": False, "error": "浏览器未初始化"}
    
    def find_element(self, selector: str) -> dict:
        """查找元素"""
        if self.element_locator:
            element = self.element_locator.find_element(selector)
            return {"success": element is not None, "element": element}
        return {"success": False, "error": "浏览器未初始化"}
    
    def click(self, selector: str) -> bool:
        """点击元素"""
        if self.element_locator:
            return self.element_locator.click_element(selector)
        return False
    
    def input_text(self, selector: str, text: str) -> bool:
        """输入文本"""
        if self.element_locator:
            return self.element_locator.input_text(selector, text)
        return False
    
    def get_text(self, selector: str) -> str:
        """获取元素文本"""
        if self.element_locator:
            return self.element_locator.get_element_text(selector)
        return ""
    
    def get_source(self) -> str:
        """获取页面源码"""
        if self.page_controller:
            return self.page_controller.get_page_source()
        return ""
    
    def screenshot_page(self, path: str = None) -> str:
        """截取页面"""
        if self.screenshot_handler:
            return self.screenshot_handler.capture_full_page(path)
        return ""
    
    def screenshot_element(self, selector: str, path: str = None) -> str:
        """截取元素"""
        if self.screenshot_handler:
            return self.screenshot_handler.capture_element(selector, path)
        return ""
    
    def save_profile(self, name: str) -> bool:
        """保存浏览器配置文件"""
        return self.browser_manager.save_browser_profile(name)
    
    def load_profile(self, name: str) -> bool:
        """加载浏览器配置文件"""
        return self.browser_manager.load_browser_profile(name)
    
    def get_fingerprint(self) -> dict:
        """获取浏览器指纹"""
        if self.browser_manager.page:
            return self.fingerprint_manager.get_current_fingerprint(self.browser_manager.page)
        return {}
    
    def set_fingerprint(self, fingerprint: dict) -> bool:
        """设置浏览器指纹"""
        if self.browser_manager.page:
            return self.fingerprint_manager.apply_fingerprint(self.browser_manager.page, fingerprint)
        return False