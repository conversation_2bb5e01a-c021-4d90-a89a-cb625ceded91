{"timestamp":"2025-07-26T08:32:59.453688","level":"INFO","logger":"test_api_api","message":"API请求: GET /test","module":"logger","function":"_log","line":230,"category":"api","user_id":"test_user"}
{"timestamp":"2025-07-26T08:33:11.795989","level":"INFO","logger":"middleware_api","message":"中间件设置完成，限流: 60 请求/分钟","module":"logger","function":"_log","line":230,"category":"api"}
{"timestamp":"2025-07-26T08:33:27.069698","level":"INFO","logger":"middleware_api","message":"中间件设置完成，限流: 60 请求/分钟","module":"logger","function":"_log","line":230,"category":"api"}
{"timestamp":"2025-07-26T08:34:03.709759","level":"INFO","logger":"middleware_api","message":"中间件设置完成，限流: 60 请求/分钟","module":"logger","function":"_log","line":230,"category":"api"}
