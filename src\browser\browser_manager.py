"""
浏览器管理器
负责浏览器的创建、配置和生命周期管理
支持指纹浏览器环境的保存和恢复
"""

import os
import json
import shutil
from pathlib import Path
from typing import Dict, Optional, Any, List
from DrissionPage import ChromiumPage, ChromiumOptions
import logging

logger = logging.getLogger("browser")


class BrowserManager:
    """浏览器管理器 - 负责浏览器的创建、配置和生命周期管理"""
    
    def __init__(self, config: Optional[Dict] = None):
        """初始化浏览器管理器"""
        self.config = config or self._get_default_config()
        self.page: Optional[ChromiumPage] = None
        self.profile_dir = Path(self.config.get('profile_dir', './profiles'))
        self.profile_dir.mkdir(exist_ok=True)
        self.current_profile = None
        self.is_initialized = False
        
        logger.info("浏览器管理器初始化完成")
    
    def _get_default_config(self) -> Dict:
        """获取默认配置"""
        return {
            'headless': False,
            'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
            'window_size': [1920, 1080],
            'timeout': 30,
            'profile_dir': './profiles',
            'proxy': {'enabled': False, 'server': '', 'username': '', 'password': ''}
        }
    
    def create_browser(self, profile_name: Optional[str] = None, headless: Optional[bool] = None) -> ChromiumPage:
        """创建浏览器实例"""
        try:
            # 设置ChromiumOptions
            options = ChromiumOptions()
            
            # 基础配置
            if headless is not None:
                options.headless(headless)
            elif self.config.get('headless'):
                options.headless(True)
            
            # 用户代理
            if self.config.get('user_agent'):
                options.set_user_agent(self.config['user_agent'])
            
            # 窗口大小
            window_size = self.config.get('window_size', [1920, 1080])
            options.set_window_size(*window_size)
            
            # 配置文件目录
            if profile_name:
                profile_path = self.profile_dir / profile_name
                profile_path.mkdir(exist_ok=True)
                options.set_user_data_path(str(profile_path))
                self.current_profile = profile_name
            
            # 代理设置
            proxy_config = self.config.get('proxy', {})
            if proxy_config.get('enabled') and proxy_config.get('server'):
                options.set_proxy(proxy_config['server'])
            
            # 其他选项
            options.add_argument('--no-sandbox')
            options.add_argument('--disable-dev-shm-usage')
            options.add_argument('--disable-blink-features=AutomationControlled')
            options.add_experimental_option('excludeSwitches', ['enable-automation'])
            options.add_experimental_option('useAutomationExtension', False)
            
            # 创建页面
            self.page = ChromiumPage(addr_or_opts=options)
            self.is_initialized = True
            
            # 执行反检测脚本
            self._setup_anti_detection()
            
            logger.info(f"浏览器创建成功，配置文件: {profile_name or '默认'}")
            return self.page
            
        except Exception as e:
            logger.error(f"创建浏览器失败: {str(e)}")
            raise
    
    def _setup_anti_detection(self):
        """设置反检测脚本"""
        if not self.page:
            return
        
        anti_detection_script = """
        Object.defineProperty(navigator, 'webdriver', {
            get: () => undefined,
        });
        
        Object.defineProperty(navigator, 'plugins', {
            get: () => [1, 2, 3, 4, 5],
        });
        
        Object.defineProperty(navigator, 'languages', {
            get: () => ['zh-CN', 'zh', 'en'],
        });
        
        window.chrome = {
            runtime: {},
        };
        """
        
        try:
            self.page.run_js(anti_detection_script)
            logger.debug("反检测脚本执行成功")
        except Exception as e:
            logger.warning(f"反检测脚本执行失败: {str(e)}")
    
    def save_browser_profile(self, profile_name: str) -> bool:
        """保存浏览器配置文件（指纹信息）"""
        try:
            if not self.page:
                logger.error("浏览器未初始化，无法保存配置文件")
                return False
            
            profile_path = self.profile_dir / profile_name
            profile_path.mkdir(exist_ok=True)
            
            # 保存cookies
            cookies = self.page.cookies()
            cookies_file = profile_path / "cookies.json"
            with open(cookies_file, 'w', encoding='utf-8') as f:
                json.dump(cookies, f, ensure_ascii=False, indent=2)
            
            # 保存localStorage
            local_storage = self.page.run_js("return JSON.stringify(localStorage);")
            if local_storage:
                storage_file = profile_path / "localStorage.json"
                with open(storage_file, 'w', encoding='utf-8') as f:
                    f.write(local_storage)
            
            # 保存指纹信息
            fingerprint = self.get_browser_fingerprint()
            fingerprint_file = profile_path / "fingerprint.json"
            with open(fingerprint_file, 'w', encoding='utf-8') as f:
                json.dump(fingerprint, f, ensure_ascii=False, indent=2)
            
            logger.info(f"浏览器配置文件保存成功: {profile_name}")
            return True
            
        except Exception as e:
            logger.error(f"保存浏览器配置文件失败: {str(e)}")
            return False
    
    def load_browser_profile(self, profile_name: str) -> bool:
        """加载浏览器配置文件"""
        try:
            profile_path = self.profile_dir / profile_name
            if not profile_path.exists():
                logger.warning(f"配置文件不存在: {profile_name}")
                return False
            
            if not self.page:
                logger.error("浏览器未初始化，无法加载配置文件")
                return False
            
            # 加载cookies
            cookies_file = profile_path / "cookies.json"
            if cookies_file.exists():
                with open(cookies_file, 'r', encoding='utf-8') as f:
                    cookies = json.load(f)
                for cookie in cookies:
                    self.page.set.cookies(cookie)
            
            # 加载localStorage
            storage_file = profile_path / "localStorage.json"
            if storage_file.exists():
                with open(storage_file, 'r', encoding='utf-8') as f:
                    local_storage = f.read()
                self.page.run_js(f"Object.assign(localStorage, {local_storage});")
            
            # 加载指纹信息
            fingerprint_file = profile_path / "fingerprint.json"
            if fingerprint_file.exists():
                with open(fingerprint_file, 'r', encoding='utf-8') as f:
                    fingerprint = json.load(f)
                self.set_browser_fingerprint(fingerprint)
            
            self.current_profile = profile_name
            logger.info(f"浏览器配置文件加载成功: {profile_name}")
            return True
            
        except Exception as e:
            logger.error(f"加载浏览器配置文件失败: {str(e)}")
            return False
    
    def get_browser_fingerprint(self) -> Dict:
        """获取浏览器指纹信息"""
        if not self.page:
            return {}
        
        try:
            fingerprint_script = """
            return {
                userAgent: navigator.userAgent,
                language: navigator.language,
                languages: navigator.languages,
                platform: navigator.platform,
                cookieEnabled: navigator.cookieEnabled,
                doNotTrack: navigator.doNotTrack,
                hardwareConcurrency: navigator.hardwareConcurrency,
                maxTouchPoints: navigator.maxTouchPoints,
                screenWidth: screen.width,
                screenHeight: screen.height,
                screenColorDepth: screen.colorDepth,
                screenPixelDepth: screen.pixelDepth,
                timezoneOffset: new Date().getTimezoneOffset(),
                timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
                webglVendor: (() => {
                    const canvas = document.createElement('canvas');
                    const gl = canvas.getContext('webgl');
                    return gl ? gl.getParameter(gl.VENDOR) : '';
                })(),
                webglRenderer: (() => {
                    const canvas = document.createElement('canvas');
                    const gl = canvas.getContext('webgl');
                    return gl ? gl.getParameter(gl.RENDERER) : '';
                })()
            };
            """
            
            fingerprint = self.page.run_js(fingerprint_script)
            return fingerprint or {}
            
        except Exception as e:
            logger.error(f"获取浏览器指纹失败: {str(e)}")
            return {}
    
    def set_browser_fingerprint(self, fingerprint: Dict) -> bool:
        """设置浏览器指纹信息"""
        if not self.page or not fingerprint:
            return False
        
        try:
            # 设置用户代理
            if fingerprint.get('userAgent'):
                self.page.run_js(f"""
                Object.defineProperty(navigator, 'userAgent', {{
                    get: () => '{fingerprint["userAgent"]}',
                }});
                """)
            
            # 设置语言
            if fingerprint.get('language'):
                self.page.run_js(f"""
                Object.defineProperty(navigator, 'language', {{
                    get: () => '{fingerprint["language"]}',
                }});
                """)
            
            # 设置平台
            if fingerprint.get('platform'):
                self.page.run_js(f"""
                Object.defineProperty(navigator, 'platform', {{
                    get: () => '{fingerprint["platform"]}',
                }});
                """)
            
            # 设置屏幕信息
            if fingerprint.get('screenWidth') and fingerprint.get('screenHeight'):
                self.page.run_js(f"""
                Object.defineProperty(screen, 'width', {{
                    get: () => {fingerprint["screenWidth"]},
                }});
                Object.defineProperty(screen, 'height', {{
                    get: () => {fingerprint["screenHeight"]},
                }});
                """)
            
            logger.info("浏览器指纹设置成功")
            return True
            
        except Exception as e:
            logger.error(f"设置浏览器指纹失败: {str(e)}")
            return False
    
    def navigate_to(self, url: str) -> Dict:
        """导航到指定URL"""
        if not self.page:
            return {"success": False, "error": "浏览器未初始化"}
        
        try:
            self.page.get(url)
            current_url = self.page.url
            title = self.page.title
            
            logger.info(f"导航成功: {url}")
            return {
                "success": True,
                "url": current_url,
                "title": title
            }
            
        except Exception as e:
            logger.error(f"导航失败: {str(e)}")
            return {"success": False, "error": str(e)}
    
    def close_browser(self):
        """关闭浏览器"""
        try:
            if self.page:
                self.page.quit()
                self.page = None
                self.is_initialized = False
                logger.info("浏览器已关闭")
        except Exception as e:
            logger.error(f"关闭浏览器失败: {str(e)}")
    
    def get_page_source(self) -> str:
        """获取页面源码"""
        if not self.page:
            return ""
        
        try:
            return self.page.html
        except Exception as e:
            logger.error(f"获取页面源码失败: {str(e)}")
            return ""
    
    def get_status(self) -> Dict:
        """获取浏览器状态"""
        return {
            "initialized": self.is_initialized,
            "current_profile": self.current_profile,
            "current_url": self.page.url if self.page else None,
            "title": self.page.title if self.page else None
        }
    
    def list_profiles(self) -> List[str]:
        """列出所有配置文件"""
        try:
            profiles = []
            for item in self.profile_dir.iterdir():
                if item.is_dir():
                    profiles.append(item.name)
            return profiles
        except Exception as e:
            logger.error(f"列出配置文件失败: {str(e)}")
            return []
    
    def delete_profile(self, profile_name: str) -> bool:
        """删除配置文件"""
        try:
            profile_path = self.profile_dir / profile_name
            if profile_path.exists():
                shutil.rmtree(profile_path)
                logger.info(f"配置文件删除成功: {profile_name}")
                return True
            else:
                logger.warning(f"配置文件不存在: {profile_name}")
                return False
        except Exception as e:
            logger.error(f"删除配置文件失败: {str(e)}")
            return False