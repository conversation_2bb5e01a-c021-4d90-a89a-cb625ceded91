"""
浏览器组件功能测试脚本
测试浏览器管理器、指纹管理、代理管理等核心功能
"""

import sys
import os
import time
import json
from pathlib import Path

# 添加src目录到Python路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from browser import BrowserManager

def test_browser_basic_functions():
    """测试浏览器基础功能"""
    print("=" * 60)
    print("🔍 测试浏览器基础功能")
    print("=" * 60)

    # 初始化浏览器管理器
    browser_manager = BrowserManager()

    try:
        # 1. 创建浏览器实例
        print("1. 创建浏览器实例...")
        page = browser_manager.create_browser(profile_name="test_profile")
        if page:
            print("✅ 浏览器创建成功")
        else:
            print("❌ 浏览器创建失败")
            return False

        # 2. 测试页面导航
        print("\n2. 测试页面导航...")
        result = browser_manager.navigate_to("https://httpbin.org/user-agent")
        if result.get("success"):
            print(f"✅ 导航成功: {result.get('title', 'N/A')}")
        else:
            print(f"❌ 导航失败: {result.get('error', 'Unknown error')}")

        # 3. 获取页面源码
        print("\n3. 获取页面源码...")
        source = browser_manager.get_page_source()
        if source and len(source) > 100:
            print(f"✅ 页面源码获取成功，长度: {len(source)}")
        else:
            print("❌ 页面源码获取失败")

        # 4. 测试浏览器状态
        print("\n4. 检查浏览器状态...")
        status = browser_manager.get_status()
        print(f"   - 初始化状态: {status['initialized']}")
        print(f"   - 当前配置文件: {status['current_profile']}")
        print(f"   - 当前URL: {status['current_url']}")

        return True

    except Exception as e:
        print(f"❌ 浏览器基础功能测试失败: {str(e)}")
        return False
    finally:
        browser_manager.close_browser()

def test_fingerprint_functions():
    """测试指纹管理功能"""
    print("\n" + "=" * 60)
    print("🔐 测试指纹管理功能")
    print("=" * 60)

    browser_manager = BrowserManager()

    try:
        # 创建浏览器
        page = browser_manager.create_browser(profile_name="fingerprint_test")

        # 1. 获取当前指纹
        print("1. 获取当前浏览器指纹...")
        fingerprint = browser_manager.get_browser_fingerprint()
        if fingerprint:
            print("✅ 指纹获取成功")
            print(f"   - User Agent: {fingerprint.get('userAgent', 'N/A')[:50]}...")
            print(f"   - 语言: {fingerprint.get('language', 'N/A')}")
            print(f"   - 平台: {fingerprint.get('platform', 'N/A')}")
            print(f"   - 屏幕分辨率: {fingerprint.get('screenWidth', 'N/A')}x{fingerprint.get('screenHeight', 'N/A')}")
            print(f"   - 时区: {fingerprint.get('timezone', 'N/A')}")
        else:
            print("❌ 指纹获取失败")
            return False

        # 2. 保存指纹配置
        print("\n2. 保存指纹配置...")
        save_result = browser_manager.save_browser_profile("test_fingerprint")
        if save_result:
            print("✅ 指纹配置保存成功")
        else:
            print("❌ 指纹配置保存失败")

        # 3. 测试指纹检测页面
        print("\n3. 访问指纹检测网站...")
        browser_manager.navigate_to("https://httpbin.org/headers")
        time.sleep(2)

        # 获取页面内容检查User-Agent
        source = browser_manager.get_page_source()
        if "User-Agent" in source:
            print("✅ 成功访问指纹检测页面")
        else:
            print("❌ 指纹检测页面访问失败")

        # 4. 测试指纹修改
        print("\n4. 测试指纹修改...")
        custom_fingerprint = {
            'userAgent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'language': 'en-US',
            'platform': 'Win32'
        }

        modify_result = browser_manager.set_browser_fingerprint(custom_fingerprint)
        if modify_result:
            print("✅ 指纹修改成功")

            # 验证修改效果
            browser_manager.navigate_to("https://httpbin.org/headers")
            time.sleep(2)
            new_source = browser_manager.get_page_source()
            if "Chrome/120.0.0.0" in new_source:
                print("✅ 指纹修改验证成功")
            else:
                print("⚠️ 指纹修改可能未生效")
        else:
            print("❌ 指纹修改失败")

        return True

    except Exception as e:
        print(f"❌ 指纹管理功能测试失败: {str(e)}")
        return False
    finally:
        browser_manager.close_browser()

def test_screenshot_functions():
    """测试截图功能"""
    print("\n" + "=" * 60)
    print("📸 测试截图功能")
    print("=" * 60)

    browser_manager = BrowserManager()

    try:
        # 创建浏览器
        page = browser_manager.create_browser()

        # 导航到测试页面
        browser_manager.navigate_to("https://httpbin.org/")
        time.sleep(2)

        # 1. 使用DrissionPage内置截图功能
        print("1. 测试页面截图...")
        try:
            screenshot_path = "screenshots/test_page.png"
            Path("screenshots").mkdir(exist_ok=True)
            page.get_screenshot(screenshot_path)
            if Path(screenshot_path).exists():
                print(f"✅ 页面截图成功: {screenshot_path}")
            else:
                print("❌ 页面截图失败")
        except Exception as e:
            print(f"⚠️ 页面截图测试失败: {str(e)}")

        return True

    except Exception as e:
        print(f"❌ 截图功能测试失败: {str(e)}")
        return False
    finally:
        browser_manager.close_browser()

def test_element_locator_functions():
    """测试元素定位功能"""
    print("\n" + "=" * 60)
    print("🎯 测试元素定位功能")
    print("=" * 60)

    browser_manager = BrowserManager()

    try:
        # 创建浏览器
        page = browser_manager.create_browser()

        # 导航到测试页面
        browser_manager.navigate_to("https://httpbin.org/forms/post")
        time.sleep(2)

        # 1. 测试元素查找
        print("1. 测试元素查找...")
        form_element = page.ele("form")
        if form_element:
            print("✅ 表单元素查找成功")
        else:
            print("❌ 表单元素查找失败")

        # 2. 测试多个元素查找
        print("\n2. 测试多个元素查找...")
        input_elements = page.eles("input")
        if input_elements:
            print(f"✅ 找到 {len(input_elements)} 个输入元素")
        else:
            print("❌ 输入元素查找失败")

        # 3. 测试文本输入
        print("\n3. 测试文本输入...")
        try:
            input_element = page.ele("input[name='custname']")
            if input_element:
                input_element.input("测试用户")
                print("✅ 文本输入成功")
            else:
                print("❌ 找不到输入元素")
        except Exception as e:
            print(f"⚠️ 文本输入测试失败: {str(e)}")

        return True

    except Exception as e:
        print(f"❌ 元素定位功能测试失败: {str(e)}")
        return False
    finally:
        browser_manager.close_browser()

def test_profile_management():
    """测试配置文件管理"""
    print("\n" + "=" * 60)
    print("📁 测试配置文件管理")
    print("=" * 60)

    browser_manager = BrowserManager()

    try:
        # 1. 创建带配置文件的浏览器
        print("1. 创建带配置文件的浏览器...")
        page = browser_manager.create_browser(profile_name="test_profile_mgmt")
        if page:
            print("✅ 带配置文件的浏览器创建成功")
        else:
            print("❌ 带配置文件的浏览器创建失败")
            return False

        # 2. 访问页面并设置cookies
        print("\n2. 访问页面并设置状态...")
        browser_manager.navigate_to("https://httpbin.org/cookies/set/test_cookie/test_value")
        time.sleep(2)

        # 3. 保存配置文件
        print("\n3. 保存配置文件...")
        save_result = browser_manager.save_browser_profile("test_profile_mgmt")
        if save_result:
            print("✅ 配置文件保存成功")
        else:
            print("❌ 配置文件保存失败")

        # 4. 关闭浏览器
        browser_manager.close_browser()

        # 5. 重新创建浏览器并加载配置文件
        print("\n4. 重新创建浏览器并加载配置文件...")
        page = browser_manager.create_browser(profile_name="test_profile_mgmt")
        load_result = browser_manager.load_browser_profile("test_profile_mgmt")
        if load_result:
            print("✅ 配置文件加载成功")
        else:
            print("❌ 配置文件加载失败")

        # 6. 验证配置文件效果
        print("\n5. 验证配置文件效果...")
        browser_manager.navigate_to("https://httpbin.org/cookies")
        time.sleep(2)
        source = browser_manager.get_page_source()
        if "test_cookie" in source:
            print("✅ 配置文件状态恢复成功")
        else:
            print("⚠️ 配置文件状态可能未完全恢复")

        # 7. 列出所有配置文件
        print("\n6. 列出所有配置文件...")
        profiles = browser_manager.list_profiles()
        if profiles:
            print(f"✅ 找到 {len(profiles)} 个配置文件: {profiles}")
        else:
            print("⚠️ 未找到配置文件")

        return True

    except Exception as e:
        print(f"❌ 配置文件管理测试失败: {str(e)}")
        return False
    finally:
        browser_manager.close_browser()

def main():
    """主测试函数"""
    print("🚀 开始浏览器组件全流程测试")
    print("测试时间:", time.strftime("%Y-%m-%d %H:%M:%S"))

    # 确保必要的目录存在
    Path("screenshots").mkdir(exist_ok=True)
    Path("profiles").mkdir(exist_ok=True)

    test_results = []

    # 执行各项测试
    tests = [
        ("浏览器基础功能", test_browser_basic_functions),
        ("指纹管理功能", test_fingerprint_functions),
        ("截图功能", test_screenshot_functions),
        ("元素定位功能", test_element_locator_functions),
        ("配置文件管理", test_profile_management)
    ]

    for test_name, test_func in tests:
        try:
            result = test_func()
            test_results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name}测试异常: {str(e)}")
            test_results.append((test_name, False))

    # 输出测试总结
    print("\n" + "=" * 60)
    print("📊 测试结果总结")
    print("=" * 60)

    passed = 0
    total = len(test_results)

    for test_name, result in test_results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name}: {status}")
        if result:
            passed += 1

    print(f"\n总体结果: {passed}/{total} 项测试通过")

    if passed == total:
        print("🎉 所有测试通过！浏览器组件功能正常")
    elif passed > total // 2:
        print("⚠️ 大部分测试通过，存在部分问题需要修复")
    else:
        print("❌ 多项测试失败，需要检查浏览器组件配置")

if __name__ == "__main__":
    main()