"""
截图处理器
负责页面和元素截图功能
"""

import os
import time
from pathlib import Path
from typing import Dict, Optional, Union, Tuple
from DrissionPage import ChromiumPage
from DrissionPage._elements.chromium_element import ChromiumElement
try:
    from PIL import Image, ImageDraw, ImageFont
    PIL_AVAILABLE = True
except ImportError:
    PIL_AVAILABLE = False

try:
    import cv2
    import numpy as np
    CV2_AVAILABLE = True
except ImportError:
    CV2_AVAILABLE = False
import logging

logger = logging.getLogger("browser")


class ScreenshotHandler:
    """截图处理器 - 负责页面和元素截图"""
    
    def __init__(self, page: ChromiumPage, save_dir: str = "./screenshots"):
        """初始化截图处理器
        
        Args:
            page: ChromiumPage实例
            save_dir: 截图保存目录
        """
        self.page = page
        self.save_dir = Path(save_dir)
        self.save_dir.mkdir(exist_ok=True)
        
        logger.info(f"截图处理器初始化完成，保存目录: {self.save_dir}")
    
    def capture_full_page(self, save_path: Optional[str] = None, format: str = "png") -> str:
        """截取整个页面
        
        Args:
            save_path: 保存路径，为None时自动生成
            format: 图片格式 (png/jpg/jpeg)
            
        Returns:
            str: 截图文件路径
        """
        try:
            if not save_path:
                timestamp = int(time.time())
                save_path = self.save_dir / f"fullpage_{timestamp}.{format}"
            else:
                save_path = Path(save_path)
                save_path.parent.mkdir(parents=True, exist_ok=True)
            
            # 截取整个页面
            self.page.get_screenshot(str(save_path), full_page=True)
            
            logger.info(f"全页面截图成功: {save_path}")
            return str(save_path)
            
        except Exception as e:
            logger.error(f"全页面截图失败: {str(e)}")
            return ""
    
    def capture_viewport(self, save_path: Optional[str] = None, format: str = "png") -> str:
        """截取可视区域
        
        Args:
            save_path: 保存路径
            format: 图片格式
            
        Returns:
            str: 截图文件路径
        """
        try:
            if not save_path:
                timestamp = int(time.time())
                save_path = self.save_dir / f"viewport_{timestamp}.{format}"
            else:
                save_path = Path(save_path)
                save_path.parent.mkdir(parents=True, exist_ok=True)
            
            # 截取可视区域
            self.page.get_screenshot(str(save_path), full_page=False)
            
            logger.info(f"可视区域截图成功: {save_path}")
            return str(save_path)
            
        except Exception as e:
            logger.error(f"可视区域截图失败: {str(e)}")
            return ""
    
    def capture_element(self, element: Union[ChromiumElement, str], save_path: Optional[str] = None, 
                       format: str = "png", padding: int = 0) -> str:
        """截取指定元素
        
        Args:
            element: 元素对象或选择器
            save_path: 保存路径
            format: 图片格式
            padding: 边距
            
        Returns:
            str: 截图文件路径
        """
        try:
            # 获取元素
            if isinstance(element, str):
                from .element_locator import ElementLocator
                locator = ElementLocator(self.page)
                element = locator.find_element(element)
                if not element:
                    logger.error(f"未找到元素: {element}")
                    return ""
            
            if not save_path:
                timestamp = int(time.time())
                save_path = self.save_dir / f"element_{timestamp}.{format}"
            else:
                save_path = Path(save_path)
                save_path.parent.mkdir(parents=True, exist_ok=True)
            
            # 滚动到元素
            element.scroll.to_see()
            time.sleep(0.5)
            
            # 获取元素位置和大小
            rect = element.rect
            
            # 截取整个页面
            temp_path = self.save_dir / f"temp_{int(time.time())}.png"
            self.page.get_screenshot(str(temp_path), full_page=False)
            
            # 裁剪元素区域
            with Image.open(temp_path) as img:
                # 计算裁剪区域
                left = max(0, rect.x - padding)
                top = max(0, rect.y - padding)
                right = min(img.width, rect.x + rect.width + padding)
                bottom = min(img.height, rect.y + rect.height + padding)
                
                # 裁剪图片
                cropped = img.crop((left, top, right, bottom))
                cropped.save(save_path)
            
            # 删除临时文件
            if temp_path.exists():
                temp_path.unlink()
            
            logger.info(f"元素截图成功: {save_path}")
            return str(save_path)
            
        except Exception as e:
            logger.error(f"元素截图失败: {str(e)}")
            return ""
    
    def capture_area(self, x: int, y: int, width: int, height: int, 
                    save_path: Optional[str] = None, format: str = "png") -> str:
        """截取指定区域
        
        Args:
            x: 左上角x坐标
            y: 左上角y坐标
            width: 宽度
            height: 高度
            save_path: 保存路径
            format: 图片格式
            
        Returns:
            str: 截图文件路径
        """
        try:
            if not save_path:
                timestamp = int(time.time())
                save_path = self.save_dir / f"area_{timestamp}.{format}"
            else:
                save_path = Path(save_path)
                save_path.parent.mkdir(parents=True, exist_ok=True)
            
            # 截取整个页面
            temp_path = self.save_dir / f"temp_{int(time.time())}.png"
            self.page.get_screenshot(str(temp_path), full_page=False)
            
            # 裁剪指定区域
            with Image.open(temp_path) as img:
                cropped = img.crop((x, y, x + width, y + height))
                cropped.save(save_path)
            
            # 删除临时文件
            if temp_path.exists():
                temp_path.unlink()
            
            logger.info(f"区域截图成功: {save_path}")
            return str(save_path)
            
        except Exception as e:
            logger.error(f"区域截图失败: {str(e)}")
            return ""
    
    def compare_screenshots(self, img1_path: str, img2_path: str, threshold: float = 0.95) -> Dict:
        """比较两张截图的相似度
        
        Args:
            img1_path: 第一张图片路径
            img2_path: 第二张图片路径
            threshold: 相似度阈值
            
        Returns:
            Dict: 比较结果
        """
        try:
            # 读取图片
            img1 = cv2.imread(img1_path)
            img2 = cv2.imread(img2_path)
            
            if img1 is None or img2 is None:
                return {"success": False, "error": "无法读取图片"}
            
            # 调整图片大小一致
            height = min(img1.shape[0], img2.shape[0])
            width = min(img1.shape[1], img2.shape[1])
            
            img1_resized = cv2.resize(img1, (width, height))
            img2_resized = cv2.resize(img2, (width, height))
            
            # 计算结构相似性
            img1_gray = cv2.cvtColor(img1_resized, cv2.COLOR_BGR2GRAY)
            img2_gray = cv2.cvtColor(img2_resized, cv2.COLOR_BGR2GRAY)
            
            # 使用模板匹配计算相似度
            result = cv2.matchTemplate(img1_gray, img2_gray, cv2.TM_CCOEFF_NORMED)
            similarity = float(result.max())
            
            is_similar = similarity >= threshold
            
            logger.info(f"图片比较完成，相似度: {similarity:.4f}")
            
            return {
                "success": True,
                "similarity": similarity,
                "is_similar": is_similar,
                "threshold": threshold
            }
            
        except Exception as e:
            logger.error(f"图片比较失败: {str(e)}")
            return {"success": False, "error": str(e)}
    
    def add_annotation(self, image_path: str, annotations: list, save_path: Optional[str] = None) -> str:
        """在截图上添加标注
        
        Args:
            image_path: 原图路径
            annotations: 标注列表，格式: [{"type": "rect/text", "x": x, "y": y, "width": w, "height": h, "text": "text", "color": "red"}]
            save_path: 保存路径
            
        Returns:
            str: 标注后的图片路径
        """
        try:
            if not save_path:
                timestamp = int(time.time())
                save_path = self.save_dir / f"annotated_{timestamp}.png"
            else:
                save_path = Path(save_path)
                save_path.parent.mkdir(parents=True, exist_ok=True)
            
            # 打开图片
            with Image.open(image_path) as img:
                draw = ImageDraw.Draw(img)
                
                # 尝试加载字体
                try:
                    font = ImageFont.truetype("arial.ttf", 16)
                except:
                    font = ImageFont.load_default()
                
                for annotation in annotations:
                    ann_type = annotation.get("type", "rect")
                    color = annotation.get("color", "red")
                    
                    if ann_type == "rect":
                        # 绘制矩形
                        x = annotation.get("x", 0)
                        y = annotation.get("y", 0)
                        width = annotation.get("width", 100)
                        height = annotation.get("height", 100)
                        
                        draw.rectangle([x, y, x + width, y + height], outline=color, width=2)
                    
                    elif ann_type == "text":
                        # 绘制文本
                        x = annotation.get("x", 0)
                        y = annotation.get("y", 0)
                        text = annotation.get("text", "")
                        
                        draw.text((x, y), text, fill=color, font=font)
                    
                    elif ann_type == "circle":
                        # 绘制圆形
                        x = annotation.get("x", 0)
                        y = annotation.get("y", 0)
                        radius = annotation.get("radius", 10)
                        
                        draw.ellipse([x - radius, y - radius, x + radius, y + radius], 
                                   outline=color, width=2)
                
                # 保存标注后的图片
                img.save(save_path)
            
            logger.info(f"图片标注成功: {save_path}")
            return str(save_path)
            
        except Exception as e:
            logger.error(f"图片标注失败: {str(e)}")
            return ""
    
    def create_gif_from_screenshots(self, image_paths: list, save_path: Optional[str] = None, 
                                  duration: int = 500) -> str:
        """从多张截图创建GIF动画
        
        Args:
            image_paths: 图片路径列表
            save_path: 保存路径
            duration: 每帧持续时间（毫秒）
            
        Returns:
            str: GIF文件路径
        """
        try:
            if not save_path:
                timestamp = int(time.time())
                save_path = self.save_dir / f"animation_{timestamp}.gif"
            else:
                save_path = Path(save_path)
                save_path.parent.mkdir(parents=True, exist_ok=True)
            
            # 加载所有图片
            images = []
            for path in image_paths:
                if Path(path).exists():
                    img = Image.open(path)
                    images.append(img)
            
            if not images:
                logger.error("没有有效的图片文件")
                return ""
            
            # 创建GIF
            images[0].save(
                save_path,
                save_all=True,
                append_images=images[1:],
                duration=duration,
                loop=0
            )
            
            logger.info(f"GIF创建成功: {save_path}")
            return str(save_path)
            
        except Exception as e:
            logger.error(f"创建GIF失败: {str(e)}")
            return ""
    
    def get_screenshot_info(self, image_path: str) -> Dict:
        """获取截图信息
        
        Args:
            image_path: 图片路径
            
        Returns:
            Dict: 图片信息
        """
        try:
            with Image.open(image_path) as img:
                return {
                    "path": image_path,
                    "size": img.size,
                    "format": img.format,
                    "mode": img.mode,
                    "file_size": Path(image_path).stat().st_size
                }
        except Exception as e:
            logger.error(f"获取截图信息失败: {str(e)}")
            return {}
    
    def batch_capture_elements(self, selectors: list, save_dir: Optional[str] = None) -> list:
        """批量截取元素
        
        Args:
            selectors: 选择器列表
            save_dir: 保存目录
            
        Returns:
            list: 截图文件路径列表
        """
        if save_dir:
            save_dir = Path(save_dir)
            save_dir.mkdir(parents=True, exist_ok=True)
        else:
            save_dir = self.save_dir
        
        screenshot_paths = []
        
        for i, selector in enumerate(selectors):
            try:
                save_path = save_dir / f"element_{i}_{int(time.time())}.png"
                path = self.capture_element(selector, str(save_path))
                screenshot_paths.append(path)
                time.sleep(0.5)  # 避免截图过快
            except Exception as e:
                logger.error(f"批量截图失败: {selector}, {str(e)}")
                screenshot_paths.append("")
        
        return screenshot_paths
    
    def cleanup_old_screenshots(self, days: int = 7) -> int:
        """清理旧截图文件
        
        Args:
            days: 保留天数
            
        Returns:
            int: 删除的文件数量
        """
        try:
            import time
            current_time = time.time()
            cutoff_time = current_time - (days * 24 * 60 * 60)
            
            deleted_count = 0
            for file_path in self.save_dir.glob("*.png"):
                if file_path.stat().st_mtime < cutoff_time:
                    file_path.unlink()
                    deleted_count += 1
            
            for file_path in self.save_dir.glob("*.jpg"):
                if file_path.stat().st_mtime < cutoff_time:
                    file_path.unlink()
                    deleted_count += 1
            
            logger.info(f"清理旧截图完成，删除 {deleted_count} 个文件")
            return deleted_count
            
        except Exception as e:
            logger.error(f"清理旧截图失败: {str(e)}")
            return 0
