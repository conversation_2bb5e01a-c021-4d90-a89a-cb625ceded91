"""
页面控制器
负责页面导航和基本操作
"""

import time
from typing import Dict, Optional, Any
from DrissionPage import ChromiumPage
import logging

logger = logging.getLogger("browser")


class PageController:
    """页面控制器 - 负责页面导航和基本操作"""
    
    def __init__(self, page: ChromiumPage):
        """初始化页面控制器
        
        Args:
            page: ChromiumPage实例
        """
        self.page = page
        self.default_timeout = 30
        
    def navigate_to(self, url: str, wait_time: float = 2.0) -> Dict:
        """导航到指定URL
        
        Args:
            url: 目标URL
            wait_time: 等待时间
            
        Returns:
            Dict: 导航结果
        """
        try:
            logger.info(f"导航到: {url}")
            self.page.get(url)
            
            # 等待页面加载
            if wait_time > 0:
                time.sleep(wait_time)
            
            current_url = self.page.url
            title = self.page.title
            
            return {
                "success": True,
                "url": current_url,
                "title": title,
                "status_code": 200
            }
            
        except Exception as e:
            logger.error(f"导航失败: {str(e)}")
            return {
                "success": False,
                "error": str(e),
                "url": url
            }
    
    def get_page_source(self) -> str:
        """获取页面源码
        
        Returns:
            str: 页面HTML源码
        """
        try:
            return self.page.html
        except Exception as e:
            logger.error(f"获取页面源码失败: {str(e)}")
            return ""
    
    def get_page_text(self) -> str:
        """获取页面文本内容
        
        Returns:
            str: 页面文本内容
        """
        try:
            return self.page.text
        except Exception as e:
            logger.error(f"获取页面文本失败: {str(e)}")
            return ""
    
    def wait_for_load(self, timeout: int = None) -> bool:
        """等待页面加载完成
        
        Args:
            timeout: 超时时间（秒）
            
        Returns:
            bool: 是否加载完成
        """
        timeout = timeout or self.default_timeout
        
        try:
            # 等待页面加载完成
            self.page.wait.load_start(timeout)
            return True
        except Exception as e:
            logger.warning(f"等待页面加载超时: {str(e)}")
            return False
    
    def wait_for_element(self, selector: str, timeout: int = None) -> bool:
        """等待元素出现
        
        Args:
            selector: 元素选择器
            timeout: 超时时间（秒）
            
        Returns:
            bool: 元素是否出现
        """
        timeout = timeout or self.default_timeout
        
        try:
            element = self.page.wait.ele_loaded(selector, timeout)
            return element is not None
        except Exception as e:
            logger.warning(f"等待元素出现超时: {selector}, {str(e)}")
            return False
    
    def execute_script(self, script: str) -> Any:
        """执行JavaScript脚本
        
        Args:
            script: JavaScript代码
            
        Returns:
            Any: 脚本执行结果
        """
        try:
            result = self.page.run_js(script)
            logger.debug(f"脚本执行成功: {script[:50]}...")
            return result
        except Exception as e:
            logger.error(f"脚本执行失败: {str(e)}")
            return None
    
    def scroll_to_bottom(self, pause_time: float = 1.0) -> bool:
        """滚动到页面底部
        
        Args:
            pause_time: 滚动间隔时间
            
        Returns:
            bool: 是否成功
        """
        try:
            # 获取页面高度
            last_height = self.page.run_js("return document.body.scrollHeight")
            
            while True:
                # 滚动到底部
                self.page.run_js("window.scrollTo(0, document.body.scrollHeight);")
                
                # 等待新内容加载
                time.sleep(pause_time)
                
                # 计算新的页面高度
                new_height = self.page.run_js("return document.body.scrollHeight")
                
                if new_height == last_height:
                    break
                    
                last_height = new_height
            
            logger.debug("滚动到页面底部完成")
            return True
            
        except Exception as e:
            logger.error(f"滚动到底部失败: {str(e)}")
            return False
    
    def scroll_to_element(self, selector: str) -> bool:
        """滚动到指定元素
        
        Args:
            selector: 元素选择器
            
        Returns:
            bool: 是否成功
        """
        try:
            element = self.page.ele(selector)
            if element:
                element.scroll.to_see()
                logger.debug(f"滚动到元素: {selector}")
                return True
            else:
                logger.warning(f"元素不存在: {selector}")
                return False
        except Exception as e:
            logger.error(f"滚动到元素失败: {str(e)}")
            return False
    
    def handle_alert(self, action: str = "accept", text: str = None) -> bool:
        """处理弹窗
        
        Args:
            action: 操作类型 (accept/dismiss)
            text: 输入文本（用于prompt弹窗）
            
        Returns:
            bool: 是否成功处理
        """
        try:
            if action == "accept":
                if text:
                    self.page.handle_alert(accept=True, send=text)
                else:
                    self.page.handle_alert(accept=True)
            else:
                self.page.handle_alert(accept=False)
            
            logger.debug(f"弹窗处理成功: {action}")
            return True
            
        except Exception as e:
            logger.error(f"处理弹窗失败: {str(e)}")
            return False
    
    def refresh_page(self) -> bool:
        """刷新页面
        
        Returns:
            bool: 是否成功
        """
        try:
            self.page.refresh()
            logger.debug("页面刷新成功")
            return True
        except Exception as e:
            logger.error(f"页面刷新失败: {str(e)}")
            return False
    
    def go_back(self) -> bool:
        """后退
        
        Returns:
            bool: 是否成功
        """
        try:
            self.page.back()
            logger.debug("页面后退成功")
            return True
        except Exception as e:
            logger.error(f"页面后退失败: {str(e)}")
            return False
    
    def go_forward(self) -> bool:
        """前进
        
        Returns:
            bool: 是否成功
        """
        try:
            self.page.forward()
            logger.debug("页面前进成功")
            return True
        except Exception as e:
            logger.error(f"页面前进失败: {str(e)}")
            return False
    
    def get_current_url(self) -> str:
        """获取当前URL
        
        Returns:
            str: 当前URL
        """
        try:
            return self.page.url
        except Exception as e:
            logger.error(f"获取当前URL失败: {str(e)}")
            return ""
    
    def get_page_title(self) -> str:
        """获取页面标题
        
        Returns:
            str: 页面标题
        """
        try:
            return self.page.title
        except Exception as e:
            logger.error(f"获取页面标题失败: {str(e)}")
            return ""
    
    def get_cookies(self) -> list:
        """获取页面cookies
        
        Returns:
            list: cookies列表
        """
        try:
            return self.page.cookies()
        except Exception as e:
            logger.error(f"获取cookies失败: {str(e)}")
            return []
    
    def set_cookies(self, cookies: list) -> bool:
        """设置cookies
        
        Args:
            cookies: cookies列表
            
        Returns:
            bool: 是否成功
        """
        try:
            for cookie in cookies:
                self.page.set.cookies(cookie)
            logger.debug("设置cookies成功")
            return True
        except Exception as e:
            logger.error(f"设置cookies失败: {str(e)}")
            return False
    
    def clear_cookies(self) -> bool:
        """清除cookies
        
        Returns:
            bool: 是否成功
        """
        try:
            self.page.clear_cookies()
            logger.debug("清除cookies成功")
            return True
        except Exception as e:
            logger.error(f"清除cookies失败: {str(e)}")
            return False
    
    def set_viewport_size(self, width: int, height: int) -> bool:
        """设置视口大小
        
        Args:
            width: 宽度
            height: 高度
            
        Returns:
            bool: 是否成功
        """
        try:
            self.page.set.window.size(width, height)
            logger.debug(f"设置视口大小成功: {width}x{height}")
            return True
        except Exception as e:
            logger.error(f"设置视口大小失败: {str(e)}")
            return False
    
    def get_page_info(self) -> Dict:
        """获取页面信息
        
        Returns:
            Dict: 页面信息
        """
        return {
            "url": self.get_current_url(),
            "title": self.get_page_title(),
            "cookies_count": len(self.get_cookies()),
            "page_source_length": len(self.get_page_source())
        }