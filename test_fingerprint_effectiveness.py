"""
指纹有效性深度测试
测试指纹修改是否真的能够改变浏览器的检测特征
"""

import sys
import os
import time
import json
from pathlib import Path

# 添加src目录到Python路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from browser import BrowserManager

def test_fingerprint_effectiveness():
    """测试指纹修改的有效性"""
    print("🔍 深度测试指纹修改有效性")
    print("=" * 60)
    
    browser_manager = BrowserManager()
    
    try:
        # 1. 创建浏览器并获取原始指纹
        print("1. 获取原始浏览器指纹...")
        page = browser_manager.create_browser(profile_name="fingerprint_test")
        
        # 访问指纹检测网站
        browser_manager.navigate_to("https://httpbin.org/headers")
        time.sleep(2)
        
        original_source = browser_manager.get_page_source()
        original_fingerprint = browser_manager.get_browser_fingerprint()
        
        print(f"   原始User-Agent: {original_fingerprint.get('userAgent', 'N/A')}")
        print(f"   原始语言: {original_fingerprint.get('language', 'N/A')}")
        print(f"   原始平台: {original_fingerprint.get('platform', 'N/A')}")
        
        # 2. 修改指纹
        print("\n2. 修改浏览器指纹...")
        custom_fingerprint = {
            'userAgent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'language': 'en-US',
            'platform': 'MacIntel'
        }
        
        modify_result = browser_manager.set_browser_fingerprint(custom_fingerprint)
        print(f"   指纹修改结果: {'成功' if modify_result else '失败'}")
        
        # 3. 验证指纹修改效果
        print("\n3. 验证指纹修改效果...")
        
        # 重新访问检测页面
        browser_manager.navigate_to("https://httpbin.org/headers")
        time.sleep(2)
        
        modified_source = browser_manager.get_page_source()
        new_fingerprint = browser_manager.get_browser_fingerprint()
        
        print(f"   修改后User-Agent: {new_fingerprint.get('userAgent', 'N/A')}")
        print(f"   修改后语言: {new_fingerprint.get('language', 'N/A')}")
        print(f"   修改后平台: {new_fingerprint.get('platform', 'N/A')}")
        
        # 4. 检查HTTP头中的User-Agent
        print("\n4. 检查HTTP请求头...")
        if "Chrome/120.0.0.0" in modified_source:
            print("✅ HTTP头中的User-Agent已成功修改")
        else:
            print("❌ HTTP头中的User-Agent未修改")
            print("   可能原因：指纹修改只影响JavaScript检测，不影响HTTP头")
        
        # 5. 测试JavaScript指纹检测
        print("\n5. 测试JavaScript指纹检测...")
        
        # 执行JavaScript获取navigator信息
        js_result = page.run_js("""
            return {
                userAgent: navigator.userAgent,
                language: navigator.language,
                platform: navigator.platform,
                languages: navigator.languages,
                cookieEnabled: navigator.cookieEnabled,
                hardwareConcurrency: navigator.hardwareConcurrency
            };
        """)
        
        if js_result:
            print(f"   JS检测User-Agent: {js_result.get('userAgent', 'N/A')}")
            print(f"   JS检测语言: {js_result.get('language', 'N/A')}")
            print(f"   JS检测平台: {js_result.get('platform', 'N/A')}")
            
            # 检查是否成功修改
            if "Chrome/120.0.0.0" in js_result.get('userAgent', ''):
                print("✅ JavaScript指纹检测显示修改成功")
            else:
                print("❌ JavaScript指纹检测显示修改失败")
        else:
            print("❌ JavaScript执行失败")
        
        # 6. 测试WebGL指纹
        print("\n6. 测试WebGL指纹...")
        webgl_result = page.run_js("""
            try {
                const canvas = document.createElement('canvas');
                const gl = canvas.getContext('webgl') || canvas.getContext('experimental-webgl');
                if (gl) {
                    return {
                        vendor: gl.getParameter(gl.VENDOR),
                        renderer: gl.getParameter(gl.RENDERER),
                        version: gl.getParameter(gl.VERSION),
                        shadingLanguageVersion: gl.getParameter(gl.SHADING_LANGUAGE_VERSION)
                    };
                }
                return null;
            } catch(e) {
                return null;
            }
        """)
        
        if webgl_result:
            print(f"   WebGL供应商: {webgl_result.get('vendor', 'N/A')}")
            print(f"   WebGL渲染器: {webgl_result.get('renderer', 'N/A')}")
        else:
            print("   WebGL信息获取失败")
        
        # 7. 测试Canvas指纹
        print("\n7. 测试Canvas指纹...")
        canvas_result = page.run_js("""
            try {
                const canvas = document.createElement('canvas');
                const ctx = canvas.getContext('2d');
                ctx.textBaseline = 'top';
                ctx.font = '14px Arial';
                ctx.fillText('Canvas fingerprint test 🔍', 2, 2);
                return canvas.toDataURL().substring(0, 50) + '...';
            } catch(e) {
                return null;
            }
        """)
        
        if canvas_result:
            print(f"   Canvas指纹: {canvas_result}")
        else:
            print("   Canvas指纹获取失败")
        
        return True
        
    except Exception as e:
        print(f"❌ 指纹有效性测试失败: {str(e)}")
        return False
    finally:
        browser_manager.close_browser()

def test_profile_persistence():
    """测试配置文件持久性"""
    print("\n" + "=" * 60)
    print("💾 测试配置文件持久性")
    print("=" * 60)
    
    browser_manager = BrowserManager()
    
    try:
        # 1. 创建浏览器并设置特定状态
        print("1. 创建浏览器并设置状态...")
        page = browser_manager.create_browser(profile_name="persistence_test")
        
        # 访问设置cookie的页面
        browser_manager.navigate_to("https://httpbin.org/cookies/set/test_persistence/12345")
        time.sleep(2)
        
        # 保存配置文件
        save_result = browser_manager.save_browser_profile("persistence_test")
        print(f"   配置文件保存: {'成功' if save_result else '失败'}")
        
        # 关闭浏览器
        browser_manager.close_browser()
        
        # 2. 重新创建浏览器并加载配置文件
        print("\n2. 重新创建浏览器并加载配置文件...")
        page = browser_manager.create_browser(profile_name="persistence_test")
        load_result = browser_manager.load_browser_profile("persistence_test")
        print(f"   配置文件加载: {'成功' if load_result else '失败'}")
        
        # 3. 验证状态是否恢复
        print("\n3. 验证状态恢复...")
        browser_manager.navigate_to("https://httpbin.org/cookies")
        time.sleep(2)
        
        source = browser_manager.get_page_source()
        if "test_persistence" in source and "12345" in source:
            print("✅ Cookie状态成功恢复")
        else:
            print("❌ Cookie状态未恢复")
        
        # 4. 检查localStorage
        print("\n4. 检查localStorage恢复...")
        # 设置localStorage
        page.run_js("localStorage.setItem('test_key', 'test_value');")
        
        # 保存配置文件
        browser_manager.save_browser_profile("persistence_test")
        browser_manager.close_browser()
        
        # 重新加载
        page = browser_manager.create_browser(profile_name="persistence_test")
        browser_manager.load_browser_profile("persistence_test")
        
        # 检查localStorage
        stored_value = page.run_js("return localStorage.getItem('test_key');")
        if stored_value == "test_value":
            print("✅ localStorage状态成功恢复")
        else:
            print("❌ localStorage状态未恢复")
        
        return True
        
    except Exception as e:
        print(f"❌ 配置文件持久性测试失败: {str(e)}")
        return False
    finally:
        browser_manager.close_browser()

def main():
    """主测试函数"""
    print("🚀 开始指纹有效性深度测试")
    print("测试时间:", time.strftime("%Y-%m-%d %H:%M:%S"))
    
    # 确保必要的目录存在
    Path("profiles").mkdir(exist_ok=True)
    
    test_results = []
    
    # 执行测试
    tests = [
        ("指纹修改有效性", test_fingerprint_effectiveness),
        ("配置文件持久性", test_profile_persistence)
    ]
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            test_results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name}测试异常: {str(e)}")
            test_results.append((test_name, False))
    
    # 输出测试总结
    print("\n" + "=" * 60)
    print("📊 深度测试结果总结")
    print("=" * 60)
    
    passed = 0
    total = len(test_results)
    
    for test_name, result in test_results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n总体结果: {passed}/{total} 项测试通过")

if __name__ == "__main__":
    main()
